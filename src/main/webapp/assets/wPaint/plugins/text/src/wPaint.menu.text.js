(function ($) {

  // setup menu
  $.fn.wPaint.menus.text = {
    img: '//360.theolympiastone.com/assets/wPaint/plugins/text/img/icons-menu-text.png',
    items: {
      bold: {
        icon: 'toggle',
        title: 'Bold',
        index: 0,
        callback: function (toggle) { this.setFontBold(toggle); }
      },
      italic: {
        icon: 'toggle',
        title: 'Italic',
        index: 1,
        callback: function (toggle) { this.setFontItalic(toggle); }
      },
      fontSize: {
        title: 'Font Size',
        icon: 'select',
        range: [8, 9, 10, 12, 14, 16, 20, 24, 30],
        value: 12,
        callback: function (size) { this.setFontSize(size); }
      },
      fontFamily: {
        icon: 'select',
        title: 'Font Family',
        range: ['Arial', 'Courier', 'Times', 'Verdana'],
        useRange: true,
        value: 'Arial',
        callback: function (family) { this.setFontFamily(family); }
      }
    }
  };

  // add icon to main menu
  $.fn.wPaint.menus.main.items.text = {
    icon: 'menu',
    after: 'pencil',
    title: 'Text',
    index: 7,
    callback: function () { this.setMode('text'); }
  };

  // extend defaults
  $.extend($.fn.wPaint.defaults, {
    fontSize       : '12',    // current font size for text input
    fontFamily     : 'Arial', // active font family for text input
    fontBold       : false,   // text input bold enable/disable
    fontItalic     : false,   // text input italic enable/disable
    fontUnderline  : false    // text input italic enable/disable
  });

  // extend functions
  $.fn.wPaint.extend({
    generate: function () {
      this.$textCalc = $('<div></div>').hide();

      // make sure clicking on the text-tnput doesn't trigger another textInput
      this.$textInput = $('<textarea class="wPaint-text-input" spellcheck="false"></textarea>')
          .on('mousedown', this._stopPropagation)
          .css({position: 'absolute'})
          .hide();

      $('body').append(this.$textCalc);
      this.$el.append(this.$textInput);

      this.menus.all.text = this._createMenu('text');
    },

    _init: function () {
      var _this = this;

      function inputClick() {
        _this._drawTextIfNotEmpty();
        _this.$textInput.hide();
        _this.$canvasTemp.hide();
      }

      // in case we click on another element while typing - just auto set the text
      for (var i in this.menus.all) {
        this.menus.all[i].$menu
            .on('click', inputClick)
            .on('mousedown', this._stopPropagation);
      }

      // same idea here for clicking outside of the canvas area
      $(document).on('mousedown', inputClick);
    },

    /****************************************
     * setters
     ****************************************/
    setFillStyle: function (fillStyle) {
      this.$textInput.css('color', fillStyle);
    },

    setFontSize: function (size) {
      this.options.fontSize = parseInt(size, 10);
      this._setFont({fontSize: size + 'px', lineHeight: size + 'px'});
      this.menus.all.text._setSelectValue('fontSize', size);
    },

    setFontFamily: function (family) {
      this.options.fontFamily = family;
      this._setFont({fontFamily: family});
      this.menus.all.text._setSelectValue('fontFamily', family);
    },

    setFontBold: function (bold) {
      this.options.fontBold = bold;
      this._setFont({fontWeight: (bold ? 'bold' : '')});
    },

    setFontItalic: function (italic) {
      this.options.fontItalic = italic;
      this._setFont({fontStyle: (italic ? 'italic' : '')});
    },

    setFontUnderline: function (underline) {
      this.options.fontUnderline = underline;
      this._setFont({fontWeight: (underline ? 'underline' : '')});
    },

    _setFont: function (css) {
      this.$textInput.css(css);
      this.$textCalc.css(css);
    },

    /****************************************
     * Text
     ****************************************/
    _drawTextDown: function (e) {
      this._drawTextIfNotEmpty();
      this._drawShapeDown(e, 1);

      this.$textInput
          .css({left: e.pageX - 1, top: e.pageY - 1, width: 0, height: 0})
          .show().focus();
    },

    _drawTextMove: function (e) {
      this._drawShapeMove(e, 1);

      this.$textInput.css({left: e.left - 1, top: e.top - 1, width: e.width, height: e.height});
    },

    _drawTextIfNotEmpty: function () {
      if (this.$textInput.val() !== '') { this._drawText(); }
    },

    // 修改后的 _drawText 函数
    _drawText: function () {
      var fontString = '',
          lines = this.$textInput.val().split('\n'),
          textInputWidth = this.$textInput.width() - 2,
          offset = this.$textInput.position(),
          left = offset.left + 1,
          top = offset.top + 1,
          maxWidth = 0,
          i, ii;

      if (this.options.fontItalic) { fontString += 'italic '; }
      if (this.options.fontBold) { fontString += 'bold '; }

      fontString += this.options.fontSize + 'px ' + this.options.fontFamily;

      // 计算所有行中最长的宽度
      for (i = 0, ii = lines.length; i < ii; i++) {
        var lineWidth = this.$textCalc.text(lines[i]).width();
        if (lineWidth > maxWidth) {
          maxWidth = lineWidth;
        }
      }

      // 设置文本框的宽度为最大行的宽度
      this.$textInput.width(maxWidth + 2);

      // 绘制每一行文本
      for (i = 0, ii = lines.length; i < ii; i++) {
        this.ctx.fillStyle = this.options.fillStyle;
        this.ctx.textBaseline = 'top';
        this.ctx.font = fontString;
        this.ctx.fillText(lines[i], left, top);

        top += this.options.fontSize;
      }

      this.$textInput.val('');
      this._addUndo();
    }
  });
})(jQuery);
