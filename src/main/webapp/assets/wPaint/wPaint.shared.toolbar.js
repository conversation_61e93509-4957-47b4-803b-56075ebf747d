/**
 * wPaint 共享工具栏管理器
 * 实现多个画布共享一个工具栏的功能
 */

(function($) {
    'use strict';
    
    // 共享工具栏管理器
    window.WPaintSharedToolbar = {
        
        // 配置选项
        config: {
            toolbarId: 'wPaint-shared-toolbar',
            canvasSelectorId: 'wPaint-canvas-selector',
            currentCanvasIndex: 0,
            canvases: [],
            toolbarInstance: null,
            isDragging: false
        },
        
        /**
         * 初始化共享工具栏
         * @param {Object} options 配置选项
         */
        init: function(options) {
            options = options || {};
            
            // 合并配置
            $.extend(this.config, options);
            
            // 创建工具栏容器
            this._createToolbarContainer();
            
            // 创建画布选择器
            this._createCanvasSelector();
            
            // 初始化拖拽功能
            this._initDraggable();
            
            return this;
        },
        
        /**
         * 添加画布到共享工具栏管理
         * @param {jQuery} $canvas 画布元素
         * @param {Object} wPaintOptions wPaint配置选项
         * @param {String} canvasName 画布名称
         */
        addCanvas: function($canvas, wPaintOptions, canvasName) {
            var canvasIndex = this.config.canvases.length;

            // 检查画布是否已经初始化wPaint
            var isAlreadyInitialized = $canvas.data('wPaint') !== undefined;

            if (!isAlreadyInitialized && wPaintOptions) {
                // 如果还没初始化，则初始化wPaint
                var sharedOptions = $.extend({}, wPaintOptions, {
                    menuHandle: false
                });
                $canvas.wPaint(sharedOptions);
            }

            // 添加到画布列表
            this.config.canvases.push({
                $canvas: $canvas,
                name: canvasName || 'Canvas ' + (canvasIndex + 1),
                index: canvasIndex,
                wPaintOptions: wPaintOptions || {}
            });

            // 更新画布选择器
            this._updateCanvasSelector();

            // 如果是第一个画布，创建共享工具栏
            if (canvasIndex === 0) {
                this._createSharedToolbar($canvas);
                this._activateCanvas(0);
            }

            // 添加画布点击事件
            this._addCanvasClickHandler($canvas, canvasIndex);

            return this;
        },
        
        /**
         * 激活指定画布
         * @param {Number} canvasIndex 画布索引
         */
        activateCanvas: function(canvasIndex) {
            if (canvasIndex >= 0 && canvasIndex < this.config.canvases.length) {
                this._activateCanvas(canvasIndex);
            }
        },
        
        /**
         * 获取当前激活的画布
         * @returns {Object} 当前画布信息
         */
        getCurrentCanvas: function() {
            return this.config.canvases[this.config.currentCanvasIndex];
        },
        
        /**
         * 创建工具栏容器
         * @private
         */
        _createToolbarContainer: function() {
            var $container = $('#' + this.config.toolbarId);
            if ($container.length === 0) {
                $container = $('<div id="' + this.config.toolbarId + '" class="wPaint-shared-toolbar-container"></div>');
                $('body').append($container);
            }
            
            // 添加样式
            $container.css({
                position: 'fixed',
                top: '20px',
                left: '20px',
                zIndex: 9999,
                background: 'rgba(255, 255, 255, 0.95)',
                border: '1px solid #ccc',
                borderRadius: '8px',
                padding: '10px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                cursor: 'move'
            });
            
            this.config.$toolbarContainer = $container;
        },
        
        /**
         * 创建画布选择器
         * @private
         */
        _createCanvasSelector: function() {
            var $selector = $('<div class="canvas-selector-wrapper">' +
                '<label>选择画布: </label>' +
                '<select id="' + this.config.canvasSelectorId + '" class="canvas-selector"></select>' +
                '</div>');
            
            $selector.css({
                marginBottom: '10px',
                fontSize: '12px'
            });
            
            this.config.$toolbarContainer.append($selector);
            this.config.$canvasSelector = $('#' + this.config.canvasSelectorId);
            
            // 绑定选择事件
            var self = this;
            this.config.$canvasSelector.on('change', function() {
                var selectedIndex = parseInt($(this).val());
                self._activateCanvas(selectedIndex);
            });
        },
        
        /**
         * 创建共享工具栏
         * @private
         */
        _createSharedToolbar: function($firstCanvas) {
            var self = this;

            // 使用第一个画布的工具栏作为模板
            var firstWPaint = $firstCanvas.data('wPaint');
            if (!firstWPaint) {
                console.error('无法获取wPaint实例');
                return;
            }

            // 等待工具栏创建完成
            setTimeout(function() {
                var $originalToolbar = $firstCanvas.find('.wPaint-menu').first();
                if ($originalToolbar.length > 0) {
                    // 克隆工具栏
                    var $clonedToolbar = $originalToolbar.clone(true);

                    // 重置样式
                    $clonedToolbar.css({
                        position: 'relative',
                        left: 'auto',
                        top: 'auto',
                        zIndex: 'auto'
                    });

                    // 添加到共享容器
                    self.config.$toolbarContainer.append($clonedToolbar);

                    // 存储工具栏实例引用
                    self.config.$sharedToolbar = $clonedToolbar;

                    // 重新绑定工具栏事件
                    self._rebindToolbarEvents();

                    // 隐藏所有原始工具栏
                    self._hideOriginalToolbars();
                }
            }, 200);
        },
        
        /**
         * 隐藏所有原始工具栏
         * @private
         */
        _hideOriginalToolbars: function() {
            var self = this;
            setTimeout(function() {
                self.config.canvases.forEach(function(canvas) {
                    var $toolbar = canvas.$canvas.find('.wPaint-menu');
                    if ($toolbar.length > 0) {
                        $toolbar.css({
                            display: 'none'
                        });
                    }
                });
            }, 300); // 给wPaint更多时间完成初始化
        },

        /**
         * 重新绑定工具栏事件
         * @private
         */
        _rebindToolbarEvents: function() {
            var self = this;
            var $toolbar = this.config.$sharedToolbar;

            if (!$toolbar) return;

            // 重新绑定所有工具栏按钮事件
            $toolbar.find('.wPaint-menu-icon').off('click.shared').on('click.shared', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var currentCanvas = self.getCurrentCanvas();
                if (currentCanvas && currentCanvas.$canvas.data('wPaint')) {
                    var $this = $(this);
                    var iconClass = $this.attr('class');
                    var wPaintInstance = currentCanvas.$canvas.data('wPaint');

                    // 根据图标类型执行相应操作
                    self._handleToolbarAction($this, wPaintInstance);
                }
            });

            // 绑定颜色选择器事件
            $toolbar.find('.wPaint-menu-colorpicker').off('change.shared').on('change.shared', function(e) {
                var currentCanvas = self.getCurrentCanvas();
                if (currentCanvas && currentCanvas.$canvas.data('wPaint')) {
                    var wPaintInstance = currentCanvas.$canvas.data('wPaint');
                    var color = $(this).val();

                    // 根据类型设置颜色
                    if ($(this).closest('.wPaint-menu-icon').hasClass('wPaint-menu-icon-fillStyle')) {
                        wPaintInstance.setFillStyle(color);
                    } else if ($(this).closest('.wPaint-menu-icon').hasClass('wPaint-menu-icon-strokeStyle')) {
                        wPaintInstance.setStrokeStyle(color);
                    }
                }
            });

            // 绑定选择框事件
            $toolbar.find('select').off('change.shared').on('change.shared', function(e) {
                var currentCanvas = self.getCurrentCanvas();
                if (currentCanvas && currentCanvas.$canvas.data('wPaint')) {
                    var wPaintInstance = currentCanvas.$canvas.data('wPaint');
                    var value = $(this).val();

                    // 根据选择框类型设置值
                    if ($(this).closest('.wPaint-menu-icon').hasClass('wPaint-menu-icon-lineWidth')) {
                        wPaintInstance.setLineWidth(value);
                    }
                }
            });
        },

        /**
         * 处理工具栏操作
         * @private
         */
        _handleToolbarAction: function($icon, wPaintInstance) {
            var iconClasses = $icon.attr('class');

            // 根据图标类型执行相应操作
            if (iconClasses.indexOf('wPaint-menu-icon-undo') !== -1) {
                wPaintInstance.undo();
            } else if (iconClasses.indexOf('wPaint-menu-icon-redo') !== -1) {
                wPaintInstance.redo();
            } else if (iconClasses.indexOf('wPaint-menu-icon-clear') !== -1) {
                wPaintInstance.clear();
            } else if (iconClasses.indexOf('wPaint-menu-icon-rectangle') !== -1) {
                wPaintInstance.setMode('rectangle');
            } else if (iconClasses.indexOf('wPaint-menu-icon-ellipse') !== -1) {
                wPaintInstance.setMode('ellipse');
            } else if (iconClasses.indexOf('wPaint-menu-icon-line') !== -1) {
                wPaintInstance.setMode('line');
            } else if (iconClasses.indexOf('wPaint-menu-icon-pencil') !== -1) {
                wPaintInstance.setMode('pencil');
            } else if (iconClasses.indexOf('wPaint-menu-icon-text') !== -1) {
                wPaintInstance.setMode('text');
            } else if (iconClasses.indexOf('wPaint-menu-icon-save') !== -1) {
                // 触发保存
                var imageData = wPaintInstance.getImage();
                if (wPaintInstance.options.saveImg) {
                    wPaintInstance.options.saveImg.call(wPaintInstance, imageData);
                }
            }

            // 更新工具栏状态
            this._updateToolbarState($icon);
        },

        /**
         * 更新工具栏状态
         * @private
         */
        _updateToolbarState: function($activeIcon) {
            var $toolbar = this.config.$sharedToolbar;
            if (!$toolbar) return;

            // 移除所有激活状态
            $toolbar.find('.wPaint-menu-icon').removeClass('wPaint-menu-icon-active');

            // 添加当前激活状态
            if ($activeIcon && $activeIcon.hasClass('wPaint-menu-icon-activate')) {
                $activeIcon.addClass('wPaint-menu-icon-active');
            }
        },

        /**
         * 激活画布
         * @private
         */
        _activateCanvas: function(canvasIndex) {
            // 取消所有画布的激活状态
            this.config.canvases.forEach(function(canvas, index) {
                canvas.$canvas.removeClass('wPaint-active-canvas');
                if (index !== canvasIndex) {
                    canvas.$canvas.css({
                        border: '2px solid transparent',
                        opacity: 0.7
                    });
                }
            });
            
            // 激活指定画布
            var activeCanvas = this.config.canvases[canvasIndex];
            if (activeCanvas) {
                activeCanvas.$canvas.addClass('wPaint-active-canvas');
                activeCanvas.$canvas.css({
                    border: '2px solid #007cba',
                    opacity: 1
                });
                
                this.config.currentCanvasIndex = canvasIndex;
                this.config.$canvasSelector.val(canvasIndex);
                
                // 同步工具栏状态
                this._syncToolbarState(activeCanvas);
            }
        },
        
        /**
         * 同步工具栏状态
         * @private
         */
        _syncToolbarState: function(activeCanvas) {
            if (!activeCanvas || !this.config.toolbarInstance) return;
            
            var wPaintInstance = activeCanvas.$canvas.data('wPaint');
            if (wPaintInstance) {
                // 同步当前模式
                this.config.toolbarInstance.setMode(wPaintInstance.options.mode);
                
                // 同步其他状态（颜色、线宽等）
                if (wPaintInstance.options.strokeStyle) {
                    this.config.toolbarInstance.setStrokeStyle(wPaintInstance.options.strokeStyle);
                }
                if (wPaintInstance.options.fillStyle) {
                    this.config.toolbarInstance.setFillStyle(wPaintInstance.options.fillStyle);
                }
                if (wPaintInstance.options.lineWidth) {
                    this.config.toolbarInstance.setLineWidth(wPaintInstance.options.lineWidth);
                }
            }
        },
        
        /**
         * 更新画布选择器
         * @private
         */
        _updateCanvasSelector: function() {
            this.config.$canvasSelector.empty();
            this.config.canvases.forEach(function(canvas, index) {
                var $option = $('<option value="' + index + '">' + canvas.name + '</option>');
                this.config.$canvasSelector.append($option);
            }.bind(this));
        },
        
        /**
         * 添加画布点击处理
         * @private
         */
        _addCanvasClickHandler: function($canvas, canvasIndex) {
            var self = this;
            $canvas.on('click.sharedToolbar', function(e) {
                if (!$(e.target).closest('.wPaint-menu').length) {
                    self._activateCanvas(canvasIndex);
                }
            });
        },
        
        /**
         * 初始化拖拽功能
         * @private
         */
        _initDraggable: function() {
            var self = this;
            var $container = this.config.$toolbarContainer;
            
            $container.on('mousedown', function(e) {
                if ($(e.target).closest('.wPaint-menu-icon, select').length > 0) {
                    return; // 不在工具按钮上拖拽
                }
                
                self.config.isDragging = true;
                var startX = e.pageX - $container.offset().left;
                var startY = e.pageY - $container.offset().top;
                
                $(document).on('mousemove.drag', function(e) {
                    if (self.config.isDragging) {
                        $container.css({
                            left: e.pageX - startX,
                            top: e.pageY - startY
                        });
                    }
                });
                
                $(document).on('mouseup.drag', function() {
                    self.config.isDragging = false;
                    $(document).off('mousemove.drag mouseup.drag');
                });
            });
        },
        
        /**
         * 销毁共享工具栏
         */
        destroy: function() {
            if (this.config.$toolbarContainer) {
                this.config.$toolbarContainer.remove();
            }
            
            // 恢复所有画布的独立工具栏
            this.config.canvases.forEach(function(canvas) {
                canvas.$canvas.off('click.sharedToolbar');
                // 可以选择重新初始化独立工具栏
            });
            
            // 重置配置
            this.config = {
                toolbarId: 'wPaint-shared-toolbar',
                canvasSelectorId: 'wPaint-canvas-selector',
                currentCanvasIndex: 0,
                canvases: [],
                toolbarInstance: null,
                isDragging: false
            };
        }
    };
    
    // 便捷函数
    window.initWPaintSharedToolbar = function(options) {
        return WPaintSharedToolbar.init(options);
    };
    
})(jQuery);
