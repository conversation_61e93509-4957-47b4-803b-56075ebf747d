/*!
 SearchPanes 1.2.1
 2019-2020 SpryMedia Ltd - datatables.net/license
*/
(function(){var g,s,t,n=function(a,b,d,c,f,h){var e=this;void 0===h&&(h=null);if(!s||!s.versionCheck||!s.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");if(!s.select)throw Error("SearchPane requires Select");a=new s.Api(a);this.classes=g.extend(!0,{},n.classes);this.c=g.extend(!0,{},n.defaults,b);this.customPaneSettings=h;this.s={cascadeRegen:!1,clearing:!1,colOpts:[],deselect:!1,displayed:!1,dt:a,dtPane:void 0,filteringActive:!1,index:d,indexes:[],lastCascade:!1,
lastSelect:!1,listSet:!1,name:void 0,redraw:!1,rowData:{arrayFilter:[],arrayOriginal:[],arrayTotals:[],bins:{},binsOriginal:{},binsTotal:{},filterMap:new Map,totalOptions:0},scrollTop:0,searchFunction:void 0,selectPresent:!1,serverSelect:[],serverSelecting:!1,showFiltered:!1,tableLength:null,updating:!1};b=a.columns().eq(0).toArray().length;this.colExists=this.s.index<b;this.c.layout=c;b=parseInt(c.split("-")[1],10);this.dom={buttonGroup:g("<div/>").addClass(this.classes.buttonGroup),clear:g('<button type="button">&#215;</button>').addClass(this.classes.dull).addClass(this.classes.paneButton).addClass(this.classes.clearButton),
container:g("<div/>").addClass(this.classes.container).addClass(this.classes.layout+(10>b?c:c.split("-")[0]+"-9")),countButton:g('<button type="button"></button>').addClass(this.classes.paneButton).addClass(this.classes.countButton),dtP:g("<table><thead><tr><th>"+(this.colExists?g(a.column(this.colExists?this.s.index:0).header()).text():this.customPaneSettings.header||"Custom Pane")+"</th><th/></tr></thead></table>"),lower:g("<div/>").addClass(this.classes.subRow2).addClass(this.classes.narrowButton),
nameButton:g('<button type="button"></button>').addClass(this.classes.paneButton).addClass(this.classes.nameButton),panesContainer:f,searchBox:g("<input/>").addClass(this.classes.paneInputButton).addClass(this.classes.search),searchButton:g('<button type = "button" class="'+this.classes.searchIcon+'"></button>').addClass(this.classes.paneButton),searchCont:g("<div/>").addClass(this.classes.searchCont),searchLabelCont:g("<div/>").addClass(this.classes.searchLabelCont),topRow:g("<div/>").addClass(this.classes.topRow),
upper:g("<div/>").addClass(this.classes.subRow1).addClass(this.classes.narrowSearch)};this.s.displayed=!1;a=this.s.dt;this.selections=[];this.s.colOpts=this.colExists?this._getOptions():this._getBonusOptions();var l=this.s.colOpts,c=g('<button type="button">X</button>').addClass(this.classes.paneButton);g(c).text(a.i18n("searchPanes.clearPane","X"));this.dom.container.addClass(l.className);this.dom.container.addClass(null!==this.customPaneSettings&&void 0!==this.customPaneSettings.className?this.customPaneSettings.className:
"");this.s.name=void 0!==this.s.colOpts.name?this.s.colOpts.name:null!==this.customPaneSettings&&void 0!==this.customPaneSettings.name?this.customPaneSettings.name:this.colExists?g(a.column(this.s.index).header()).text():this.customPaneSettings.header||"Custom Pane";g(f).append(this.dom.container);var p=a.table(0).node();this.s.searchFunction=function(a,b,c){if(0===e.selections.length||a.nTable!==p)return!0;a=null;e.colExists&&(a=b[e.s.index],"filter"!==l.orthogonal.filter&&(a=e.s.rowData.filterMap.get(c),
a instanceof g.fn.dataTable.Api&&(a=a.toArray())));return e._search(a,c)};g.fn.dataTable.ext.search.push(this.s.searchFunction);if(this.c.clear)g(c).on("click",function(){e.dom.container.find(e.classes.search).each(function(){g(this).val("");g(this).trigger("input")});e.clearPane()});a.on("draw.dtsp",function(){e._adjustTopRow()});a.on("buttons-action",function(){e._adjustTopRow()});g(window).on("resize.dtsp",s.util.throttle(function(){e._adjustTopRow()}));a.on("column-reorder.dtsp",function(a,b,
c){e.s.index=c.mapping[e.s.index]});return this};n.prototype.clearData=function(){this.s.rowData={arrayFilter:[],arrayOriginal:[],arrayTotals:[],bins:{},binsOriginal:{},binsTotal:{},filterMap:new Map,totalOptions:0}};n.prototype.clearPane=function(){this.s.dtPane.rows({selected:!0}).deselect();this.updateTable();return this};n.prototype.destroy=function(){g(this.s.dtPane).off(".dtsp");g(this.s.dt).off(".dtsp");g(this.dom.nameButton).off(".dtsp");g(this.dom.countButton).off(".dtsp");g(this.dom.clear).off(".dtsp");
g(this.dom.searchButton).off(".dtsp");g(this.dom.container).remove();for(var a=g.fn.dataTable.ext.search.indexOf(this.s.searchFunction);-1!==a;)g.fn.dataTable.ext.search.splice(a,1),a=g.fn.dataTable.ext.search.indexOf(this.s.searchFunction);void 0!==this.s.dtPane&&this.s.dtPane.destroy();this.s.listSet=!1};n.prototype.getPaneCount=function(){return void 0!==this.s.dtPane?this.s.dtPane.rows({selected:!0}).data().toArray().length:0};n.prototype.rebuildPane=function(a,b,d,c){void 0===a&&(a=!1);void 0===
b&&(b=null);void 0===d&&(d=null);void 0===c&&(c=!1);this.clearData();var f=[];this.s.serverSelect=[];var h=null;void 0!==this.s.dtPane&&(c&&(this.s.dt.page.info().serverSide?this.s.serverSelect=this.s.dtPane.rows({selected:!0}).data().toArray():f=this.s.dtPane.rows({selected:!0}).data().toArray()),this.s.dtPane.clear().destroy(),h=g(this.dom.container).prev(),this.destroy(),this.s.dtPane=void 0,g.fn.dataTable.ext.search.push(this.s.searchFunction));this.dom.container.removeClass(this.classes.hidden);
this.s.displayed=!1;this._buildPane(!this.s.dt.page.info().serverSide?f:this.s.serverSelect,a,b,d,h);return this};n.prototype.removePane=function(){this.s.displayed=!1;g(this.dom.container).hide()};n.prototype.setCascadeRegen=function(a){this.s.cascadeRegen=a};n.prototype.setClear=function(a){this.s.clearing=a};n.prototype.updatePane=function(a){void 0===a&&(a=!1);this.s.updating=!0;this._updateCommon(a);this.s.updating=!1};n.prototype.updateTable=function(){this.selections=this.s.dtPane.rows({selected:!0}).data().toArray();
this._searchExtras();(this.c.cascadePanes||this.c.viewTotal)&&this.updatePane()};n.prototype._setListeners=function(){var a=this,b=this.s.rowData,d;this.s.dtPane.on("select.dtsp",function(){clearTimeout(d);a.s.dt.page.info().serverSide&&!a.s.updating?a.s.serverSelecting||(a.s.serverSelect=a.s.dtPane.rows({selected:!0}).data().toArray(),a.s.scrollTop=g(a.s.dtPane.table().node()).parent()[0].scrollTop,a.s.selectPresent=!0,a.s.dt.draw(!1)):(g(a.dom.clear).removeClass(a.classes.dull),a.s.selectPresent=
!0,a.s.updating||a._makeSelection(),a.s.selectPresent=!1)});this.s.dtPane.on("deselect.dtsp",function(){d=setTimeout(function(){a.s.dt.page.info().serverSide&&!a.s.updating?a.s.serverSelecting||(a.s.serverSelect=a.s.dtPane.rows({selected:!0}).data().toArray(),a.s.deselect=!0,a.s.dt.draw(!1)):(a.s.deselect=!0,0===a.s.dtPane.rows({selected:!0}).data().toArray().length&&g(a.dom.clear).addClass(a.classes.dull),a._makeSelection(),a.s.deselect=!1,a.s.dt.state.save())},50)});this.s.dt.on("stateSaveParams.dtsp",
function(c,d,h){if(g.isEmptyObject(h))a.s.dtPane.state.clear();else{var c=[],e,l,p,j;void 0!==a.s.dtPane&&(c=a.s.dtPane.rows({selected:!0}).data().map(function(a){return a.filter.toString()}).toArray(),e=g(a.dom.searchBox).val(),l=a.s.dtPane.order(),p=b.binsOriginal,j=b.arrayOriginal);void 0===h.searchPanes&&(h.searchPanes={});void 0===h.searchPanes.panes&&(h.searchPanes.panes=[]);for(d=0;d<h.searchPanes.panes.length;d++)h.searchPanes.panes[d].id===a.s.index&&(h.searchPanes.panes.splice(d,1),d--);
h.searchPanes.panes.push({arrayFilter:j,bins:p,id:a.s.index,order:l,searchTerm:e,selected:c})}});this.s.dtPane.on("user-select.dtsp",function(a,b,d,e,l){l.stopPropagation()});this.s.dtPane.on("draw.dtsp",function(){a._adjustTopRow()});g(this.dom.nameButton).on("click.dtsp",function(){var b=a.s.dtPane.order()[0][1];a.s.dtPane.order([0,"asc"===b?"desc":"asc"]).draw();a.s.dt.state.save()});g(this.dom.countButton).on("click.dtsp",function(){var b=a.s.dtPane.order()[0][1];a.s.dtPane.order([1,"asc"===b?
"desc":"asc"]).draw();a.s.dt.state.save()});g(this.dom.clear).on("click.dtsp",function(){a.dom.container.find("."+a.classes.search).each(function(){g(this).val("");g(this).trigger("input")});a.clearPane()});g(this.dom.searchButton).on("click.dtsp",function(){g(a.dom.searchBox).focus()});g(this.dom.searchBox).on("input.dtsp",function(){a.s.dtPane.search(g(a.dom.searchBox).val()).draw();a.s.dt.state.save()});this.s.dt.state.save();return!0};n.prototype._addOption=function(a,b,d,c,f,h){if(Array.isArray(a)||
a instanceof s.Api)if(a instanceof s.Api&&(a=a.toArray(),b=b.toArray()),a.length===b.length)for(var e=0;e<a.length;e++)h[a[e]]?h[a[e]]++:(h[a[e]]=1,f.push({display:b[e],filter:a[e],sort:d[e],type:c[e]})),this.s.rowData.totalOptions++;else throw Error("display and filter not the same length");else"string"===typeof this.s.colOpts.orthogonal?(h[a]?h[a]++:(h[a]=1,f.push({display:b,filter:a,sort:d,type:c})),this.s.rowData.totalOptions++):f.push({display:b,filter:a,sort:d,type:c})};n.prototype._addRow=
function(a,b,d,c,f,h,e){for(var l,g=0,j=this.s.indexes;g<j.length;g++){var i=j[g];i.filter===b&&(l=i.index)}void 0===l&&(l=this.s.indexes.length,this.s.indexes.push({filter:b,index:l}));return this.s.dtPane.row.add({className:e,display:""!==a?a:!1!==this.s.colOpts.emptyMessage?this.s.colOpts.emptyMessage:this.c.emptyMessage,filter:b,index:l,shown:d,sort:""!==f?f:!1!==this.s.colOpts.emptyMessage?this.s.colOpts.emptyMessage:this.c.emptyMessage,total:c,type:h})};n.prototype._adjustTopRow=function(){var a=
this.dom.container.find("."+this.classes.subRowsContainer),b=this.dom.container.find(".dtsp-subRow1"),d=this.dom.container.find(".dtsp-subRow2"),c=this.dom.container.find("."+this.classes.topRow);(252>g(a[0]).width()||252>g(c[0]).width())&&0!==g(a[0]).width()?(g(a[0]).addClass(this.classes.narrow),g(b[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowSearch),g(d[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowButton)):(g(a[0]).removeClass(this.classes.narrow),
g(b[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowSearch),g(d[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowButton))};n.prototype._buildPane=function(a,b,d,c,f){var h=this;void 0===a&&(a=[]);void 0===b&&(b=!1);void 0===d&&(d=null);void 0===c&&(c=null);void 0===f&&(f=null);this.selections=[];var e=this.s.dt,l=e.column(this.colExists?this.s.index:0),p=this.s.colOpts,j=this.s.rowData,i=e.i18n("searchPanes.count","{total}"),n=e.i18n("searchPanes.countFiltered",
"{shown} ({total})"),k=e.state.loaded();this.s.listSet&&(k=e.state());if(this.colExists){var o=-1;if(k&&k.searchPanes&&k.searchPanes.panes)for(var m=0;m<k.searchPanes.panes.length;m++)if(k.searchPanes.panes[m].id===this.s.index){o=m;break}if((!1===p.show||void 0!==p.show&&!0!==p.show)&&-1===o)return this.dom.container.addClass(this.classes.hidden),this.s.displayed=!1;if(!0===p.show||-1!==o)this.s.displayed=!0;if(!this.s.dt.page.info().serverSide&&(null===d||null===d.searchPanes||null===d.searchPanes.options)){if(0===
j.arrayFilter.length){this._populatePane(b);this.s.rowData.totalOptions=0;this._detailsPane();if(k&&k.searchPanes&&k.searchPanes.panes&&-1===o){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}j.arrayOriginal=j.arrayTotals;j.binsOriginal=j.binsTotal}m=Object.keys(j.binsOriginal).length;b=this._uniqueRatio(m,e.rows()[0].length);if(!1===this.s.displayed&&((void 0===p.show&&null===p.threshold?b>this.c.threshold:b>p.threshold)||!0!==p.show&&1>=m)){this.dom.container.addClass(this.classes.hidden);
this.s.displayed=!1;return}this.c.viewTotal&&0===j.arrayTotals.length?(this.s.rowData.totalOptions=0,this._detailsPane()):j.binsTotal=j.bins;this.dom.container.addClass(this.classes.show);this.s.displayed=!0}else if(null!==d&&null!==d.searchPanes&&null!==d.searchPanes.options){if(void 0!==d.tableLength)this.s.tableLength=d.tableLength,this.s.rowData.totalOptions=this.s.tableLength;else if(null===this.s.tableLength||e.rows()[0].length>this.s.tableLength)this.s.tableLength=e.rows()[0].length,this.s.rowData.totalOptions=
this.s.tableLength;b=e.column(this.s.index).dataSrc();if(void 0!==d.searchPanes.options[b]){m=0;for(b=d.searchPanes.options[b];m<b.length;m++)o=b[m],this.s.rowData.arrayFilter.push({display:o.label,filter:o.value,sort:o.label,type:o.label}),this.s.rowData.bins[o.value]=this.c.viewTotal||this.c.cascadePanes?o.count:o.total,this.s.rowData.binsTotal[o.value]=o.total}m=Object.keys(j.binsTotal).length;b=this._uniqueRatio(m,this.s.tableLength);if(!1===this.s.displayed&&((void 0===p.show&&null===p.threshold?
b>this.c.threshold:b>p.threshold)||!0!==p.show&&1>=m)){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}this.s.rowData.arrayOriginal=this.s.rowData.arrayFilter;this.s.rowData.binsOriginal=this.s.rowData.bins;this.s.displayed=!0}}else this.s.displayed=!0;this._displayPane();if(!this.s.listSet)this.dom.dtP.on("stateLoadParams.dt",function(a,b,c){g.isEmptyObject(e.state.loaded())&&g.each(c,function(a){delete c[a]})});null!==f&&0<g(this.dom.panesContainer).has(f).length?g(this.dom.container).insertAfter(f):
g(this.dom.panesContainer).prepend(this.dom.container);m=g.fn.dataTable.ext.errMode;g.fn.dataTable.ext.errMode="none";f=s.Scroller;this.s.dtPane=g(this.dom.dtP).DataTable(g.extend(!0,{columnDefs:[{className:"dtsp-nameColumn",data:"display",render:function(a,b,e){if(b==="sort")return e.sort;if(b==="type")return e.type;var c;(h.s.filteringActive||h.s.showFiltered)&&h.c.viewTotal?c=n.replace(/{total}/,e.total):c=i.replace(/{total}/,e.total);for(c=c.replace(/{shown}/,e.shown);c.indexOf("{total}")!==-1;)c=
c.replace(/{total}/,e.total);for(;c.indexOf("{shown}")!==-1;)c=c.replace(/{shown}/,e.shown);b='<span class="'+h.classes.pill+'">'+c+"</span>";if(h.c.hideCount||p.hideCount)b="";return'<div class="'+h.classes.nameCont+'"><span title="'+(typeof a==="string"&&a.match(/<[^>]*>/)!==null?a.replace(/<[^>]*>/g,""):a)+'" class="'+h.classes.name+'">'+a+"</span>"+b+"</div>"},targets:0,type:void 0!==e.settings()[0].aoColumns[this.s.index]?e.settings()[0].aoColumns[this.s.index]._sManualType:null},{className:"dtsp-countColumn "+
this.classes.badgePill,data:"shown",orderData:[1,2],targets:1,visible:!1},{data:"total",targets:2,visible:!1}],deferRender:!0,dom:"t",info:!1,language:this.s.dt.settings()[0].oLanguage,paging:f?!0:!1,scrollX:!1,scrollY:"200px",scroller:f?!0:!1,select:!0,stateSave:e.settings()[0].oFeatures.bStateSave?!0:!1},this.c.dtOpts,void 0!==p?p.dtOpts:{},void 0!==this.s.colOpts.options||!this.colExists?{createdRow:function(a,b){g(a).addClass(b.className)}}:void 0,null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts?
this.customPaneSettings.dtOpts:{}));g(this.dom.dtP).addClass(this.classes.table);g(this.dom.searchBox).attr("placeholder",void 0!==p.header?p.header:this.colExists?e.settings()[0].aoColumns[this.s.index].sTitle:this.customPaneSettings.header||"Custom Pane");g.fn.dataTable.select.init(this.s.dtPane);g.fn.dataTable.ext.errMode=m;if(this.colExists){var l=(l=l.search())?l.substr(1,l.length-2).split("|"):[],q=0;j.arrayFilter.forEach(function(a){""===a.filter&&q++});m=0;for(f=j.arrayFilter.length;m<f;m++){for(var l=
!1,o=0,r=this.s.serverSelect;o<r.length;o++)b=r[o],b.filter===j.arrayFilter[m].filter&&(l=!0);if(this.s.dt.page.info().serverSide&&(!this.c.cascadePanes||this.c.cascadePanes&&0!==j.bins[j.arrayFilter[m].filter]||this.c.cascadePanes&&null!==c||l)){l=this._addRow(j.arrayFilter[m].display,j.arrayFilter[m].filter,c?j.binsTotal[j.arrayFilter[m].filter]:j.bins[j.arrayFilter[m].filter],this.c.viewTotal||c?String(j.binsTotal[j.arrayFilter[m].filter]):j.bins[j.arrayFilter[m].filter],j.arrayFilter[m].sort,
j.arrayFilter[m].type);o=0;for(r=this.s.serverSelect;o<r.length;o++)b=r[o],b.filter===j.arrayFilter[m].filter&&(this.s.serverSelecting=!0,l.select(),this.s.serverSelecting=!1)}else!this.s.dt.page.info().serverSide&&j.arrayFilter[m]&&(void 0!==j.bins[j.arrayFilter[m].filter]||!this.c.cascadePanes)?this._addRow(j.arrayFilter[m].display,j.arrayFilter[m].filter,j.bins[j.arrayFilter[m].filter],j.binsTotal[j.arrayFilter[m].filter],j.arrayFilter[m].sort,j.arrayFilter[m].type):this.s.dt.page.info().serverSide||
this._addRow("",q,q,"","","")}}s.select.init(this.s.dtPane);(void 0!==p.options||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options)&&this._getComparisonRows();this.s.dtPane.draw();this._adjustTopRow();this.s.listSet||(this._setListeners(),this.s.listSet=!0);for(c=0;c<a.length;c++)if(j=a[c],void 0!==j){m=0;for(f=this.s.dtPane.rows().indexes().toArray();m<f.length;m++)l=f[m],void 0!==this.s.dtPane.row(l).data()&&j.filter===this.s.dtPane.row(l).data().filter&&(this.s.dt.page.info().serverSide?
(this.s.serverSelecting=!0,this.s.dtPane.row(l).select(),this.s.serverSelecting=!1):this.s.dtPane.row(l).select())}this.s.dt.page.info().serverSide&&this.s.dtPane.search(g(this.dom.searchBox).val()).draw();if(k&&k.searchPanes&&k.searchPanes.panes&&(null===d||1===d.draw)){this.c.cascadePanes||this._reloadSelect(k);d=0;for(k=k.searchPanes.panes;d<k.length;d++)a=k[d],a.id===this.s.index&&(g(this.dom.searchBox).val(a.searchTerm),g(this.dom.searchBox).trigger("input"),this.s.dtPane.order(a.order).draw())}this.s.dt.state.save();
return!0};n.prototype._detailsPane=function(){var a=this.s.dt;this.s.rowData.arrayTotals=[];this.s.rowData.binsTotal={};var b=this.s.dt.settings()[0],a=a.rows().indexes();if(!this.s.dt.page.info().serverSide)for(var d=0;d<a.length;d++)this._populatePaneArray(a[d],this.s.rowData.arrayTotals,b,this.s.rowData.binsTotal)};n.prototype._displayPane=function(){var a=this.dom.container,b=this.s.colOpts,d=parseInt(this.c.layout.split("-")[1],10);g(this.dom.topRow).empty();g(this.dom.dtP).empty();g(this.dom.topRow).addClass(this.classes.topRow);
3<d&&g(this.dom.container).addClass(this.classes.smallGap);g(this.dom.topRow).addClass(this.classes.subRowsContainer);g(this.dom.upper).appendTo(this.dom.topRow);g(this.dom.lower).appendTo(this.dom.topRow);g(this.dom.searchCont).appendTo(this.dom.upper);g(this.dom.buttonGroup).appendTo(this.dom.lower);(!1===this.c.dtOpts.searching||void 0!==b.dtOpts&&!1===b.dtOpts.searching||!this.c.controls||!b.controls||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts&&void 0!==this.customPaneSettings.dtOpts.searching&&
!this.customPaneSettings.dtOpts.searching)&&g(this.dom.searchBox).attr("disabled","disabled").removeClass(this.classes.paneInputButton).addClass(this.classes.disabledButton);g(this.dom.searchBox).appendTo(this.dom.searchCont);this._searchContSetup();this.c.clear&&(this.c.controls&&b.controls)&&g(this.dom.clear).appendTo(this.dom.buttonGroup);this.c.orderable&&(b.orderable&&this.c.controls&&b.controls)&&g(this.dom.nameButton).appendTo(this.dom.buttonGroup);!this.c.hideCount&&(!b.hideCount&&this.c.orderable&&
b.orderable&&this.c.controls&&b.controls)&&g(this.dom.countButton).appendTo(this.dom.buttonGroup);g(this.dom.topRow).prependTo(this.dom.container);g(a).append(this.dom.dtP);g(a).show()};n.prototype._getBonusOptions=function(){return g.extend(!0,{},n.defaults,{orthogonal:{threshold:null},threshold:null},void 0!==this.c?this.c:{})};n.prototype._getComparisonRows=function(){var a=this.s.colOpts,a=void 0!==a.options?a.options:null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options?this.customPaneSettings.options:
void 0;if(void 0!==a){var b=this.s.dt.rows({search:"applied"}).data().toArray(),d=this.s.dt.rows({search:"applied"}),c=this.s.dt.rows().data().toArray(),f=this.s.dt.rows(),h=[];this.s.dtPane.clear();for(var e=0;e<a.length;e++){var l=a[e],g=""!==l.label?l.label:this.c.emptyMessage,j=l.className,i=g,k="function"===typeof l.value?l.value:[],n=0,o=g,m=0;if("function"===typeof l.value){for(var q=0;q<b.length;q++)l.value.call(this.s.dt,b[q],d[0][q])&&n++;for(q=0;q<c.length;q++)l.value.call(this.s.dt,c[q],
f[0][q])&&m++;"function"!==typeof k&&k.push(l.filter)}(!this.c.cascadePanes||this.c.cascadePanes&&0!==n)&&h.push(this._addRow(i,k,n,m,o,g,j))}return h}};n.prototype._getOptions=function(){return g.extend(!0,{},n.defaults,{emptyMessage:!1,orthogonal:{threshold:null},threshold:null},this.s.dt.settings()[0].aoColumns[this.s.index].searchPanes)};n.prototype._makeSelection=function(){this.updateTable();this.s.updating=!0;this.s.dt.draw();this.s.updating=!1};n.prototype._populatePane=function(a){void 0===
a&&(a=!1);var b=this.s.dt;this.s.rowData.arrayFilter=[];this.s.rowData.bins={};var d=this.s.dt.settings()[0];if(!this.s.dt.page.info().serverSide)for(var c=0,a=((this.c.cascadePanes||this.c.viewTotal)&&!this.s.clearing&&!a?b.rows({search:"applied"}).indexes():b.rows().indexes()).toArray();c<a.length;c++)this._populatePaneArray(a[c],this.s.rowData.arrayFilter,d)};n.prototype._populatePaneArray=function(a,b,d,c){void 0===c&&(c=this.s.rowData.bins);var f=this.s.colOpts;if("string"===typeof f.orthogonal)d=
d.oApi._fnGetCellData(d,a,this.s.index,f.orthogonal),this.s.rowData.filterMap.set(a,d),this._addOption(d,d,d,d,b,c);else{var h=d.oApi._fnGetCellData(d,a,this.s.index,f.orthogonal.search);null===h&&(h="");"string"===typeof h&&(h=h.replace(/<[^>]*>/g,""));this.s.rowData.filterMap.set(a,h);c[h]?c[h]++:(c[h]=1,this._addOption(h,d.oApi._fnGetCellData(d,a,this.s.index,f.orthogonal.display),d.oApi._fnGetCellData(d,a,this.s.index,f.orthogonal.sort),d.oApi._fnGetCellData(d,a,this.s.index,f.orthogonal.type),
b,c));this.s.rowData.totalOptions++}};n.prototype._reloadSelect=function(a){if(void 0!==a){for(var b,d=0;d<a.searchPanes.panes.length;d++)if(a.searchPanes.panes[d].id===this.s.index){b=d;break}if(void 0!==b)for(var d=this.s.dtPane,c=d.rows({order:"index"}).data().map(function(a){return null!==a.filter?a.filter.toString():null}).toArray(),f=0,a=a.searchPanes.panes[b].selected;f<a.length;f++){b=a[f];var h=-1;null!==b&&(h=c.indexOf(b.toString()));-1<h&&(this.s.serverSelecting=!0,d.row(h).select(),this.s.serverSelecting=
!1)}}};n.prototype._search=function(a,b){for(var d=this.s.colOpts,c=this.s.dt,f=0,h=this.selections;f<h.length;f++){var e=h[f];"string"===typeof e.filter&&(e.filter=e.filter.replaceAll("&amp;","&"));if(Array.isArray(a)){if(-1!==a.indexOf(e.filter))return!0}else if("function"===typeof e.filter)if(e.filter.call(c,c.row(b).data(),b)){if("or"===d.combiner)return!0}else{if("and"===d.combiner)return!1}else if(a===e.filter||!("string"===typeof a&&0===a.length)&&a==e.filter||null===e.filter&&"string"===typeof a&&
""===a)return!0}return"and"===d.combiner?!0:!1};n.prototype._searchContSetup=function(){this.c.controls&&this.s.colOpts.controls&&g(this.dom.searchButton).appendTo(this.dom.searchLabelCont);!1===this.c.dtOpts.searching||(!1===this.s.colOpts.dtOpts.searching||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts&&void 0!==this.customPaneSettings.dtOpts.searching&&!this.customPaneSettings.dtOpts.searching)||g(this.dom.searchLabelCont).appendTo(this.dom.searchCont)};n.prototype._searchExtras=
function(){var a=this.s.updating;this.s.updating=!0;var b=this.s.dtPane.rows({selected:!0}).data().pluck("filter").toArray(),d=b.indexOf(!1!==this.s.colOpts.emptyMessage?this.s.colOpts.emptyMessage:this.c.emptyMessage),c=g(this.s.dtPane.table().container());-1<d&&(b[d]="");0<b.length?c.addClass(this.classes.selected):0===b.length&&c.removeClass(this.classes.selected);this.s.updating=a};n.prototype._uniqueRatio=function(a,b){return 0<b&&(0<this.s.rowData.totalOptions&&!this.s.dt.page.info().serverSide||
this.s.dt.page.info().serverSide&&0<this.s.tableLength)?a/this.s.rowData.totalOptions:1};n.prototype._updateCommon=function(a){void 0===a&&(a=!1);if(!this.s.dt.page.info().serverSide&&void 0!==this.s.dtPane&&(!this.s.filteringActive||this.c.cascadePanes||!0===a)&&(!0!==this.c.cascadePanes||!0!==this.s.selectPresent)&&(!this.s.lastSelect||!this.s.lastCascade)){var b=this.s.colOpts,d=this.s.dtPane.rows({selected:!0}).data().toArray(),a=g(this.s.dtPane.table().node()).parent()[0].scrollTop,c=this.s.rowData;
this.s.dtPane.clear();if(this.colExists){0===c.arrayFilter.length?this._populatePane():this.c.cascadePanes&&this.s.dt.rows().data().toArray().length===this.s.dt.rows({search:"applied"}).data().toArray().length?(c.arrayFilter=c.arrayOriginal,c.bins=c.binsOriginal):(this.c.viewTotal||this.c.cascadePanes)&&this._populatePane();this.c.viewTotal?this._detailsPane():c.binsTotal=c.bins;this.c.viewTotal&&!this.c.cascadePanes&&(c.arrayFilter=c.arrayTotals);for(var f=function(a){if(a&&(c.bins[a.filter]!==void 0&&
c.bins[a.filter]!==0&&h.c.cascadePanes||!h.c.cascadePanes||h.s.clearing)){var b=h._addRow(a.display,a.filter,!h.c.viewTotal?c.bins[a.filter]:c.bins[a.filter]!==void 0?c.bins[a.filter]:0,h.c.viewTotal?String(c.binsTotal[a.filter]):c.bins[a.filter],a.sort,a.type),e=d.findIndex(function(b){return b.filter===a.filter});if(e!==-1){b.select();d.splice(e,1)}}},h=this,e=0,l=c.arrayFilter;e<l.length;e++)f(l[e])}if(void 0!==b.searchPanes&&void 0!==b.searchPanes.options||void 0!==b.options||null!==this.customPaneSettings&&
void 0!==this.customPaneSettings.options){f=function(a){var b=d.findIndex(function(b){if(b.display===a.data().display)return!0});-1!==b&&(a.select(),d.splice(b,1))};e=0;for(l=this._getComparisonRows();e<l.length;e++)b=l[e],f(b)}for(f=0;f<d.length;f++)b=d[f],b=this._addRow(b.display,b.filter,0,this.c.viewTotal?b.total:0,b.display,b.display),this.s.updating=!0,b.select(),this.s.updating=!1;this.s.dtPane.draw();this.s.dtPane.table().node().parentNode.scrollTop=a}};n.version="1.1.0";n.classes={buttonGroup:"dtsp-buttonGroup",
buttonSub:"dtsp-buttonSub",clear:"dtsp-clear",clearAll:"dtsp-clearAll",clearButton:"clearButton",container:"dtsp-searchPane",countButton:"dtsp-countButton",disabledButton:"dtsp-disabledButton",dull:"dtsp-dull",hidden:"dtsp-hidden",hide:"dtsp-hide",layout:"dtsp-",name:"dtsp-name",nameButton:"dtsp-nameButton",nameCont:"dtsp-nameCont",narrow:"dtsp-narrow",paneButton:"dtsp-paneButton",paneInputButton:"dtsp-paneInputButton",pill:"dtsp-pill",search:"dtsp-search",searchCont:"dtsp-searchCont",searchIcon:"dtsp-searchIcon",
searchLabelCont:"dtsp-searchButtonCont",selected:"dtsp-selected",smallGap:"dtsp-smallGap",subRow1:"dtsp-subRow1",subRow2:"dtsp-subRow2",subRowsContainer:"dtsp-subRowsContainer",title:"dtsp-title",topRow:"dtsp-topRow"};n.defaults={cascadePanes:!1,clear:!0,combiner:"or",controls:!0,container:function(a){return a.table().container()},dtOpts:{},emptyMessage:"<i>No Data</i>",hideCount:!1,layout:"columns-3",name:void 0,orderable:!0,orthogonal:{display:"display",filter:"filter",hideCount:!1,search:"filter",
show:void 0,sort:"sort",threshold:0.6,type:"type"},preSelect:[],threshold:0.6,viewTotal:!1};t=n;var i,r,u,k=function(a,b,d){var c=this;void 0===d&&(d=!1);this.regenerating=!1;if(!r||!r.versionCheck||!r.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");if(!r.select)throw Error("SearchPane requires Select");var f=new r.Api(a);this.classes=i.extend(!0,{},k.classes);this.c=i.extend(!0,{},k.defaults,b);this.dom={clearAll:i('<button type="button">Clear All</button>').addClass(this.classes.clearAll),
container:i("<div/>").addClass(this.classes.panes).text(f.i18n("searchPanes.loadMessage","Loading Search Panes...")),emptyMessage:i("<div/>").addClass(this.classes.emptyMessage),options:i("<div/>").addClass(this.classes.container),panes:i("<div/>").addClass(this.classes.container),title:i("<div/>").addClass(this.classes.title),titleRow:i("<div/>").addClass(this.classes.titleRow),wrapper:i("<div/>")};this.s={colOpts:[],dt:f,filterCount:0,filterPane:-1,page:0,panes:[],selectionList:[],serverData:{},
stateRead:!1,updating:!1};if(void 0===f.settings()[0]._searchPanes){this._getState();if(this.s.dt.page.info().serverSide)f.on("preXhr.dt",function(a,b,d){void 0===d.searchPanes&&(d.searchPanes={});a=0;for(b=c.s.selectionList;a<b.length;a++){var f=b[a],g=c.s.dt.column(f.index).dataSrc();void 0===d.searchPanes[g]&&(d.searchPanes[g]={});for(var i=0;i<f.rows.length;i++)d.searchPanes[g][i]=f.rows[i].filter}});f.on("xhr",function(a,b,d){d&&(d.searchPanes&&d.searchPanes.options)&&(c.s.serverData=d,c.s.serverData.tableLength=
d.recordsTotal,c._serverTotals())});f.settings()[0]._searchPanes=this;this.dom.clearAll.text(f.i18n("searchPanes.clearMessage","Clear All"));if(this.s.dt.settings()[0]._bInitComplete||d)this._paneDeclare(f,a,b);else f.one("preInit.dt",function(){c._paneDeclare(f,a,b)});return this}};k.prototype.clearSelections=function(){this.dom.container.find(this.classes.search).each(function(){i(this).val("");i(this).trigger("input")});for(var a=[],b=0,d=this.s.panes;b<d.length;b++){var c=d[b];void 0!==c.s.dtPane&&
a.push(c.clearPane())}this.s.dt.draw();return a};k.prototype.getNode=function(){return this.dom.container};k.prototype.rebuild=function(a,b){void 0===a&&(a=!1);void 0===b&&(b=!1);i(this.dom.emptyMessage).remove();var d=[];!1===a&&i(this.dom.panes).empty();for(var c=0,f=this.s.panes;c<f.length;c++){var h=f[c];!1!==a&&h.s.index!==a||(h.clearData(),d.push(h.rebuildPane(void 0!==this.s.selectionList[this.s.selectionList.length-1]?h.s.index===this.s.selectionList[this.s.selectionList.length-1].index:!1,
this.s.dt.page.info().serverSide?this.s.serverData:void 0,null,b)),i(this.dom.panes).append(h.dom.container))}this.s.dt.page.info().serverSide||this.s.dt.draw();this.c.cascadePanes||this.c.viewTotal?this.redrawPanes(!0):this._updateSelection();this._updateFilterCount();this._attachPaneContainer();this.s.dt.draw();return 1===d.length?d[0]:d};k.prototype.redrawPanes=function(a){void 0===a&&(a=!1);var b=this.s.dt;if(!this.s.updating&&!this.s.dt.page.info().serverSide){var d=!0,c=this.s.filterPane;if(b.rows({search:"applied"}).data().toArray().length===
b.rows().data().toArray().length)d=!1;else if(this.c.viewTotal)for(var f=0,h=this.s.panes;f<h.length;f++){var e=h[f];if(void 0!==e.s.dtPane){var g=e.s.dtPane.rows({selected:!0}).data().toArray().length;if(0===g)for(var i=0,j=this.s.selectionList;i<j.length;i++){var k=j[i];k.index===e.s.index&&0!==k.rows.length&&(g=k.rows.length)}0<g&&-1===c?c=e.s.index:0<g&&(c=null)}}h=void 0;f=[];if(this.regenerating){h=-1;1===f.length&&(h=f[0].index);a=0;for(f=this.s.panes;a<f.length;a++)if(e=f[a],void 0!==e.s.dtPane){b=
!0;e.s.filteringActive=!0;if(-1!==c&&null!==c&&c===e.s.index||!1===d||e.s.index===h)b=!1,e.s.filteringActive=!1;e.updatePane(!b?b:d)}this._updateFilterCount()}else{g=0;for(i=this.s.panes;g<i.length;g++)if(e=i[g],e.s.selectPresent){this.s.selectionList.push({index:e.s.index,rows:e.s.dtPane.rows({selected:!0}).data().toArray(),protect:!1});b.state.save();break}else e.s.deselect&&(h=e.s.index,j=e.s.dtPane.rows({selected:!0}).data().toArray(),0<j.length&&this.s.selectionList.push({index:e.s.index,rows:j,
protect:!0}));if(0<this.s.selectionList.length){b=this.s.selectionList[this.s.selectionList.length-1].index;g=0;for(i=this.s.panes;g<i.length;g++)e=i[g],e.s.lastSelect=e.s.index===b}for(e=0;e<this.s.selectionList.length;e++)if(this.s.selectionList[e].index!==h||!0===this.s.selectionList[e].protect){b=!1;for(g=e+1;g<this.s.selectionList.length;g++)this.s.selectionList[g].index===this.s.selectionList[e].index&&(b=!0);b||(f.push(this.s.selectionList[e]),this.s.selectionList[e].protect=!1)}h=-1;1===f.length&&
(h=f[0].index);g=0;for(i=this.s.panes;g<i.length;g++)if(e=i[g],void 0!==e.s.dtPane){b=!0;e.s.filteringActive=!0;if(-1!==c&&null!==c&&c===e.s.index||!1===d||e.s.index===h)b=!1,e.s.filteringActive=!1;e.updatePane(!b?!1:d)}this._updateFilterCount();if(0<f.length&&(f.length<this.s.selectionList.length||a)){this._cascadeRegen(f);b=f[f.length-1].index;c=0;for(a=this.s.panes;c<a.length;c++)e=a[c],e.s.lastSelect=e.s.index===b}else if(0<f.length){e=0;for(a=this.s.panes;e<a.length;e++)if(f=a[e],void 0!==f.s.dtPane){b=
!0;f.s.filteringActive=!0;if(-1!==c&&null!==c&&c===f.s.index||!1===d)b=!1,f.s.filteringActive=!1;f.updatePane(!b?b:d)}}}d||(this.s.selectionList=[])}};k.prototype._attach=function(){var a=this;i(this.dom.container).removeClass(this.classes.hide);i(this.dom.titleRow).removeClass(this.classes.hide);i(this.dom.titleRow).remove();i(this.dom.title).appendTo(this.dom.titleRow);this.c.clear&&(i(this.dom.clearAll).appendTo(this.dom.titleRow),i(this.dom.clearAll).on("click.dtsps",function(){a.clearSelections()}));
i(this.dom.titleRow).appendTo(this.dom.container);for(var b=0,d=this.s.panes;b<d.length;b++)i(d[b].dom.container).appendTo(this.dom.panes);i(this.dom.panes).appendTo(this.dom.container);0===i("div."+this.classes.container).length&&i(this.dom.container).prependTo(this.s.dt);return this.dom.container};k.prototype._attachExtras=function(){i(this.dom.container).removeClass(this.classes.hide);i(this.dom.titleRow).removeClass(this.classes.hide);i(this.dom.titleRow).remove();i(this.dom.title).appendTo(this.dom.titleRow);
this.c.clear&&i(this.dom.clearAll).appendTo(this.dom.titleRow);i(this.dom.titleRow).appendTo(this.dom.container);return this.dom.container};k.prototype._attachMessage=function(){var a;try{a=this.s.dt.i18n("searchPanes.emptyPanes","No SearchPanes")}catch(b){a=null}if(null===a)i(this.dom.container).addClass(this.classes.hide),i(this.dom.titleRow).removeClass(this.classes.hide);else return i(this.dom.container).removeClass(this.classes.hide),i(this.dom.titleRow).addClass(this.classes.hide),i(this.dom.emptyMessage).text(a),
this.dom.emptyMessage.appendTo(this.dom.container),this.dom.container};k.prototype._attachPaneContainer=function(){for(var a=0,b=this.s.panes;a<b.length;a++)if(!0===b[a].s.displayed)return this._attach();return this._attachMessage()};k.prototype._cascadeRegen=function(a){this.regenerating=!0;var b=-1;1===a.length&&(b=a[0].index);for(var d=0,c=this.s.panes;d<c.length;d++){var f=c[d];f.setCascadeRegen(!0);f.setClear(!0);(void 0!==f.s.dtPane&&f.s.index===b||void 0!==f.s.dtPane)&&f.clearPane();f.setClear(!1)}this._makeCascadeSelections(a);
this.s.selectionList=a;a=0;for(b=this.s.panes;a<b.length;a++)f=b[a],f.setCascadeRegen(!1);this.regenerating=!1};k.prototype._checkMessage=function(){for(var a=0,b=this.s.panes;a<b.length;a++)if(!0===b[a].s.displayed)return;return this._attachMessage()};k.prototype._getState=function(){var a=this.s.dt.state.loaded();a&&(a.searchPanes&&void 0!==a.searchPanes.selectionList)&&(this.s.selectionList=a.searchPanes.selectionList)};k.prototype._makeCascadeSelections=function(a){for(var b=0;b<a.length;b++)for(var d=
function(e){if(e.s.index===a[b].index&&void 0!==e.s.dtPane){b===a.length-1&&(e.s.lastCascade=!0);0<e.s.dtPane.rows({selected:!0}).data().toArray().length&&void 0!==e.s.dtPane&&(e.setClear(!0),e.clearPane(),e.setClear(!1));for(var d=function(a){e.s.dtPane.rows().every(function(b){e.s.dtPane.row(b).data()!==void 0&&(a!==void 0&&e.s.dtPane.row(b).data().filter===a.filter)&&e.s.dtPane.row(b).select()})},f=0,h=a[b].rows;f<h.length;f++)d(h[f]);c._updateFilterCount();e.s.lastCascade=!1}},c=this,f=0,h=this.s.panes;f<
h.length;f++)d(h[f]);this.s.dt.state.save()};k.prototype._paneDeclare=function(a,b,d){var c=this;a.columns(0<this.c.columns.length?this.c.columns:void 0).eq(0).each(function(a){c.s.panes.push(new t(b,d,a,c.c.layout,c.dom.panes))});for(var f=a.columns().eq(0).toArray().length,h=this.c.panes.length,e=0;e<h;e++)this.s.panes.push(new t(b,d,f+e,this.c.layout,this.dom.panes,this.c.panes[e]));if(0<this.c.order.length){f=this.c.order.map(function(a){return c._findPane(a)});this.dom.panes.empty();this.s.panes=
f;f=0;for(h=this.s.panes;f<h.length;f++)this.dom.panes.append(h[f].dom.container)}this.s.dt.settings()[0]._bInitComplete?this._startup(a):this.s.dt.settings()[0].aoInitComplete.push({fn:function(){c._startup(a)}})};k.prototype._findPane=function(a){for(var b=0,d=this.s.panes;b<d.length;b++){var c=d[b];if(a===c.s.name)return c}};k.prototype._serverTotals=function(){for(var a=!1,b=!1,d=this.s.dt,c=0,f=this.s.panes;c<f.length;c++){var h=f[c];if(h.s.selectPresent){this.s.selectionList.push({index:h.s.index,
rows:h.s.dtPane.rows({selected:!0}).data().toArray(),protect:!1});d.state.save();h.s.selectPresent=!1;a=!0;break}else h.s.deselect&&(b=h.s.dtPane.rows({selected:!0}).data().toArray(),0<b.length&&this.s.selectionList.push({index:h.s.index,rows:b,protect:!0}),b=a=!0)}if(a){d=[];for(c=0;c<this.s.selectionList.length;c++){h=!1;for(f=c+1;f<this.s.selectionList.length;f++)this.s.selectionList[f].index===this.s.selectionList[c].index&&(h=!0);if(!h){for(var f=!1,a=0,e=this.s.panes;a<e.length;a++)h=e[a],h.s.index===
this.s.selectionList[c].index&&0<h.s.dtPane.rows({selected:!0}).data().toArray().length&&(f=!0);f&&d.push(this.s.selectionList[c])}}this.s.selectionList=d}else this.s.selectionList=[];d=-1;if(b&&1===this.s.selectionList.length){b=0;for(c=this.s.panes;b<c.length;b++)h=c[b],h.s.lastSelect=!1,h.s.deselect=!1,void 0!==h.s.dtPane&&0<h.s.dtPane.rows({selected:!0}).data().toArray().length&&(d=h.s.index)}else if(0<this.s.selectionList.length){b=this.s.selectionList[this.s.selectionList.length-1].index;c=
0;for(f=this.s.panes;c<f.length;c++)h=f[c],h.s.lastSelect=h.s.index===b,h.s.deselect=!1}else if(0===this.s.selectionList.length){b=0;for(c=this.s.panes;b<c.length;b++)h=c[b],h.s.lastSelect=!1,h.s.deselect=!1}i(this.dom.panes).empty();b=0;for(c=this.s.panes;b<c.length;b++)h=c[b],h.s.lastSelect?h._setListeners():h.rebuildPane(void 0,this.s.dt.page.info().serverSide?this.s.serverData:void 0,h.s.index===d?!0:null,!0),i(this.dom.panes).append(h.dom.container),void 0!==h.s.dtPane&&(i(h.s.dtPane.table().node()).parent()[0].scrollTop=
h.s.scrollTop,i.fn.dataTable.select.init(h.s.dtPane));this.s.dt.page.info().serverSide||this.s.dt.draw()};k.prototype._startup=function(a){var b=this;i(this.dom.container).text("");this._attachExtras();i(this.dom.container).append(this.dom.panes);i(this.dom.panes).empty();var d=this.s.dt.state.loaded();if(this.c.viewTotal&&!this.c.cascadePanes&&null!==d&&void 0!==d&&void 0!==d.searchPanes&&void 0!==d.searchPanes.panes){for(var c=!1,f=0,h=d.searchPanes.panes;f<h.length;f++){var e=h[f];if(0<e.selected.length){c=
!0;break}}if(c){c=0;for(f=this.s.panes;c<f.length;c++)e=f[c],e.s.showFiltered=!0}}c=0;for(f=this.s.panes;c<f.length;c++)e=f[c],e.rebuildPane(void 0,0<Object.keys(this.s.serverData).length?this.s.serverData:void 0),i(this.dom.panes).append(e.dom.container);this.s.dt.page.info().serverSide||this.s.dt.draw();!this.s.stateRead&&(null!==d&&void 0!==d)&&(this.s.dt.page(d.start/this.s.dt.page.len()),this.s.dt.draw("page"));this.s.stateRead=!0;if(this.c.viewTotal&&!this.c.cascadePanes){d=0;for(c=this.s.panes;d<
c.length;d++)e=c[d],e.updatePane()}this._updateFilterCount();this._checkMessage();a.on("preDraw.dtsps",function(){b._updateFilterCount();(b.c.cascadePanes||b.c.viewTotal)&&!b.s.dt.page.info().serverSide?b.redrawPanes():b._updateSelection();b.s.filterPane=-1});this.s.dt.on("stateSaveParams.dtsp",function(a,c,e){if(e.searchPanes===void 0)e.searchPanes={};e.searchPanes.selectionList=b.s.selectionList});if(this.s.dt.page.info().serverSide)a.off("page"),a.on("page",function(){b.s.page=b.s.dt.page()}),
a.off("preXhr.dt"),a.on("preXhr.dt",function(a,c,e){if(e.searchPanes===void 0)e.searchPanes={};for(var c=a=0,d=b.s.panes;c<d.length;c++){var f=d[c],h=b.s.dt.column(f.s.index).dataSrc();e.searchPanes[h]===void 0&&(e.searchPanes[h]={});if(f.s.dtPane!==void 0)for(var f=f.s.dtPane.rows({selected:true}).data().toArray(),g=0;g<f.length;g++){e.searchPanes[h][g]=f[g].filter;a++}}b.c.viewTotal&&b._prepViewTotal();if(a>0){if(a!==b.s.filterCount){e.start=0;b.s.page=0}else e.start=b.s.page*b.s.dt.page.len();
b.s.dt.page(b.s.page);b.s.filterCount=a}});else a.on("preXhr.dt",function(){for(var a=0,c=b.s.panes;a<c.length;a++)c[a].clearData()});this.s.dt.on("xhr",function(){var a=false;if(!b.s.dt.page.info().serverSide)b.s.dt.one("preDraw",function(){if(!a){var c=b.s.dt.page();a=true;i(b.dom.panes).empty();for(var e=0,d=b.s.panes;e<d.length;e++){var f=d[e];f.clearData();f.rebuildPane(b.s.selectionList[b.s.selectionList.length-1]!==void 0?f.s.index===b.s.selectionList[b.s.selectionList.length-1].index:false,
void 0,void 0,true);i(b.dom.panes).append(f.dom.container)}b.s.dt.page.info().serverSide||b.s.dt.draw();b.c.cascadePanes||b.c.viewTotal?b.redrawPanes(b.c.cascadePanes):b._updateSelection();b._checkMessage();b.s.dt.one("draw",function(){b.s.dt.page(c).draw(false)})}})});d=0;for(c=this.s.panes;d<c.length;d++)if(e=c[d],void 0!==e&&void 0!==e.s.dtPane&&(void 0!==e.s.colOpts.preSelect&&0<e.s.colOpts.preSelect.length||null!==e.customPaneSettings&&void 0!==e.customPaneSettings.preSelect&&0<e.customPaneSettings.preSelect.length)){f=
e.s.dtPane.rows().data().toArray().length;for(h=0;h<f;h++)(-1!==e.s.colOpts.preSelect.indexOf(e.s.dtPane.cell(h,0).data())||null!==e.customPaneSettings&&void 0!==e.customPaneSettings.preSelect&&-1!==e.customPaneSettings.preSelect.indexOf(e.s.dtPane.cell(h,0).data()))&&e.s.dtPane.row(h).select();e.updateTable()}if(void 0!==this.s.selectionList&&0<this.s.selectionList.length){d=this.s.selectionList[this.s.selectionList.length-1].index;c=0;for(f=this.s.panes;c<f.length;c++)e=f[c],e.s.lastSelect=e.s.index===
d}0<this.s.selectionList.length&&this.c.cascadePanes&&this._cascadeRegen(this.s.selectionList);this._updateFilterCount();a.on("destroy.dtsps",function(){for(var c=0,e=b.s.panes;c<e.length;c++)e[c].destroy();a.off(".dtsps");i(b.dom.clearAll).off(".dtsps");i(b.dom.container).remove();b.clearSelections()});if(this.c.clear)i(this.dom.clearAll).on("click.dtsps",function(){b.clearSelections()});a.settings()[0]._searchPanes=this};k.prototype._prepViewTotal=function(){for(var a=this.s.filterPane,b=!1,d=0,
c=this.s.panes;d<c.length;d++){var f=c[d];if(void 0!==f.s.dtPane){var h=f.s.dtPane.rows({selected:!0}).data().toArray().length;0<h&&-1===a?(a=f.s.index,b=!0):0<h&&(a=null)}}d=0;for(c=this.s.panes;d<c.length;d++)if(f=c[d],void 0!==f.s.dtPane&&(f.s.filteringActive=!0,-1!==a&&null!==a&&a===f.s.index||!1===b))f.s.filteringActive=!1};k.prototype._updateFilterCount=function(){for(var a=0,b=0,d=this.s.panes;b<d.length;b++){var c=d[b];void 0!==c.s.dtPane&&(a+=c.getPaneCount())}b=this.s.dt.i18n("searchPanes.title",
"Filters Active - %d",a);i(this.dom.title).text(b);void 0!==this.c.filterChanged&&"function"===typeof this.c.filterChanged&&this.c.filterChanged.call(this.s.dt,a)};k.prototype._updateSelection=function(){this.s.selectionList=[];for(var a=0,b=this.s.panes;a<b.length;a++){var d=b[a];void 0!==d.s.dtPane&&this.s.selectionList.push({index:d.s.index,rows:d.s.dtPane.rows({selected:!0}).data().toArray(),protect:!1})}this.s.dt.state.save()};k.version="1.2.1";k.classes={clear:"dtsp-clear",clearAll:"dtsp-clearAll",
container:"dtsp-searchPanes",emptyMessage:"dtsp-emptyMessage",hide:"dtsp-hidden",panes:"dtsp-panesContainer",search:"dtsp-search",title:"dtsp-title",titleRow:"dtsp-titleRow"};k.defaults={cascadePanes:!1,clear:!0,container:function(a){return a.table().container()},columns:[],filterChanged:void 0,layout:"columns-3",order:[],panes:[],viewTotal:!1};u=k;var v=function(a,b,d){function c(a,b){void 0===b&&(b=!1);var c=new f.Api(a),d=c.init().searchPanes||f.defaults.searchPanes;return(new u(c,d,b)).getNode()}
g=a;s=a.fn.dataTable;i=a;var f=r=a.fn.dataTable;a.fn.dataTable.SearchPanes=u;a.fn.DataTable.SearchPanes=u;a.fn.dataTable.SearchPane=t;a.fn.DataTable.SearchPane=t;b=a.fn.dataTable.Api.register;b("searchPanes()",function(){return this});b("searchPanes.clearSelections()",function(){return this.iterator("table",function(a){a._searchPanes&&a._searchPanes.clearSelections()})});b("searchPanes.rebuildPane()",function(a,b){return this.iterator("table",function(c){c._searchPanes&&c._searchPanes.rebuild(a,b)})});
b("searchPanes.container()",function(){var a=this.context[0];return a._searchPanes?a._searchPanes.getNode():null});a.fn.dataTable.ext.buttons.searchPanesClear={text:"Clear Panes",action:function(a,b){b.searchPanes.clearSelections()}};a.fn.dataTable.ext.buttons.searchPanes={action:function(a,b,c,d){a.stopPropagation();this.popover(d._panes.getNode(),{align:"dt-container"});d._panes.rebuild(void 0,!0)},config:{},init:function(b,c,d){var f=new a.fn.dataTable.SearchPanes(b,a.extend({filterChanged:function(a){b.button(c).text(b.i18n("searchPanes.collapse",
{"0":"SearchPanes",_:"SearchPanes (%d)"},a))}},d.config)),g=b.i18n("searchPanes.collapse","SearchPanes",0);b.button(c).text(g);d._panes=f},text:"Search Panes"};a(d).on("preInit.dt.dtsp",function(a,b){if("dt"===a.namespace&&(b.oInit.searchPanes||f.defaults.searchPanes))b._searchPanes||c(b,!0)});f.ext.feature.push({cFeature:"P",fnInit:c});f.ext.features&&f.ext.features.register("searchPanes",c)};"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(a){return v(a,window,
document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);if(!b||!b.fn.dataTable)b=require("datatables.net")(a,b).$;return v(b,a,a.document)}:v(window.jQuery,window,document)})();
