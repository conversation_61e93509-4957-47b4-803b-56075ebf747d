#set(seoTitle="澳林匹亚管理系统")
#@layout()
#define main()

<div class="layui-row layui-hide-md"><h2 class="jf-panel-name" id="linkId">常用链接 显示</h2></div>
<!-- 包含侧边栏文件 -->
#include("_sidebar.html")
<!-- 内容容器 -->
<div class="layui-col-xs12 layui-col-md10">
    <div class="jf-panel">
        <h2 class="jf-panel-name">个人中心</h2>
        <div class="layui-tab">
            #role("WDS管理员")
            <div class="layui-row">
                <a href="/my/account/wdslist" class="layui-btn layui-btn-lg layui-btn-danger">同事账号管理</a>
                <a href="/admin/algg" class="layui-btn layui-btn-lg layui-btn-danger">公告管理</a>
            </div>
            #end
            <div class="layui-row">
                <div class="w1000" style="float: left">
                    <!--                    <a href="https://chat.theolympiastone.com" target="_blank" class="layui-btn layui-btn-lg">ChatGpt 1 密码2023</a>-->
                    <!--                    <a href="http://chat.rrcc.tv:2023" target="_blank" class="layui-btn layui-btn-lg">ChatGpt 2</a>-->
                    <a href="https://chat.openai.com" target="_blank" class="layui-btn layui-btn-lg">GPT官网(免登录)</a>
                    <!--                    <a href="http://192.168.1.25:3000" target="_blank" class="layui-btn layui-btn-lg">内网Gpt</a>-->
                    <!--                    <a href="https://al.ibooker.org.cn/assist" target="_blank" class="layui-btn layui-btn-sm">ChatGpt1</a>-->
                    <!--                    <a href="https://share.freegpts.org/list" target="_blank" class="layui-btn layui-btn-lg">共享ChatGpt</a>-->
                    <!--                    <a href="https://ai.caifree.com/" target="_blank" class="layui-btn layui-btn-sm">ChatGpt2</a>-->
                    <a href="https://tool.browser.qq.com/" target="_blank" class="layui-btn layui-btn-lg">腾讯小工具</a>
                    <!--                    <a href="https://chinagpt.p1ay.top//" target="_blank" class="layui-btn layui-btn-lg">Gpt资源</a>-->
                    <!--                    <a href="https://www.amz123.com/ai/" target="_blank" class="layui-btn layui-btn-sm">AI导航</a>-->
                    <a href="https://www.futuretools.io/" target="_blank" class="layui-btn layui-btn-sm">Future Tools</a>
                    <a href="https://ai-bot.cn/" target="_blank" class="layui-btn layui-btn-sm">AI导航</a>
                    <a href="/my/forum" target="_blank" class="layui-btn layui-btn-sm">澳林社区</a>
                    <!--                    <a href="https://www.tongtongxing.cn/" target="_blank" class="layui-btn layui-btn-sm">统统行</a>-->
                    <!--                    <a href="https://toolsdar.com/ai" target="_blank" class="layui-btn layui-btn-sm">工具达人</a>-->
                    <!--                    <a href="https://www.aizj.cc/" target="_blank" class="layui-btn layui-btn-sm">AI之家</a>-->
                    <!--                    <a href="https://ai.tboxn.com/" target="_blank" class="layui-btn layui-btn-sm">图钉AI</a>-->
                    <!--                    <a href="https://ainavtool.com/" target="_blank" class="layui-btn layui-btn-sm">Ai导航</a>-->
                    <!--                    <a href="https://www.ai-lib.club/" target="_blank" class="layui-btn layui-btn-sm">Ai库</a>-->
                    <!--                    <a href="https://www.aigcget.com/" target="_blank" class="layui-btn layui-btn-sm">AIGC</a>-->
                    <!--                    <a href="https://www.aitool123.cc/" target="_blank" class="layui-btn layui-btn-sm">AI汇</a>-->
                    <!--                    <a href="https://www.ainavpro.com/" target="_blank" class="layui-btn layui-btn-sm">AI导航</a>-->
                    <!--                    <a href="https://aitoolmall.com/zh/" target="_blank" class="layui-btn layui-btn-sm">AI工具</a>-->

                    <!--                    <a href="http://chat.dirtytalk.top" target="_blank" class="layui-btn layui-btn-lg">ChatGpt 4</a>-->
                </div>
            </div>
            <div class="layui-row" style="margin-top: 10px;">
                <div class="w200" style="float: left;">
                    <a href="/my/kq_dk/dk" class="layui-btn layui-btn-lg layui-btn-danger">打卡</a>
                    <a href="/my/kq_qj/add" class="layui-btn layui-btn-lg layui-btn-danger">请假</a>
                </div>
                <div class="w150" style="float: left;">
                    <button class="layui-btn layui-btn-lg" onclick="grjbxx()">个人基本信息</button>
                </div>
                <div class="w150 margin-left" style="float: left;">
                    <button class="layui-btn layui-btn-lg" onclick="pxzsgl()">培训与知识</button>
                </div>
                <div class="w150 margin-left" style="float: left;">
                    <button class="layui-btn layui-btn-lg" onclick="gwrcgz()">今日早晚报</button>
                </div>
                <!--                <div class="w150 margin-left">-->
                <!--                    <button class="layui-btn layui-btn-lg" onclick="zysygh()">职业生涯规划</button>-->
                <!--                </div>-->
                <div class="w150 margin-left" style="float: left;">
                    <button class="layui-btn layui-btn-lg" onclick="grsrymbgl()">工作目标管理</button>
                </div>
                <div class="w150 margin-left" style="float: left;">
                    <button class="layui-btn layui-btn-lg" onclick="jstj()">晋升途径</button>
                </div>
                <div class="w150 margin-left" style="float: left;">
                    <button class="layui-btn layui-btn-lg" onclick="jxtj()">业务统计</button>
                </div>
            </div>
        </div>
        <br>
    </div>

    <!-- 业绩指标看板 -->
    <div class="layui-card" id="performance-dashboard" style="display: none">
        <div class="layui-card-header">
            <h3>业绩指标看板 <span class="layui-badge layui-bg-blue">实时</span></h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <!-- 左侧数据摘要 -->
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header">核心指标摘要</div>
                        <div class="layui-card-body">
                            <div class="summary-item">
                                <i class="layui-icon layui-icon-user"></i>
                                <div class="summary-content">
                                    <div class="summary-title">行业核心客户数量</div>
                                    <div class="summary-value" id="core-customer-count">xxx</div>
                                    <div class="summary-compare">
                                        <span class="compare-item">公司平均: <span id="avg-core-customer">xx</span></span>
                                        <span class="compare-item">目标要求: <span id="target-core-customer">xx</span></span>
                                    </div>
                                </div>
                            </div>
                            <div class="summary-item">
                                <i class="layui-icon layui-icon-dialogue"></i>
                                <div class="summary-content">
                                    <div class="summary-title">成功联系核心客户</div>
                                    <div class="summary-value">
                                        自<span id="contact-start-date">xxx</span>日起
                                        合计<span id="contact-total">xx</span>次
                                        (日均<span id="contact-daily">x</span>次)
                                    </div>
                                    <div class="summary-compare">
                                        <span class="compare-item">公司平均: <span id="avg-contact">xx</span>次</span>
                                        <span class="compare-item">目标要求: <span id="target-contact">xx</span>次</span>
                                    </div>
                                </div>
                            </div>
                            <div class="summary-item">
                                <i class="layui-icon layui-icon-survey"></i>
                                <div class="summary-content">
                                    <div class="summary-title">计划联系潜在客户</div>
                                    <div class="summary-value">
                                        自<span id="potential-start-date">xxx</span>日起
                                        合计<span id="potential-total">xx</span>次
                                        (日均<span id="potential-daily">x</span>次)
                                    </div>
                                    <div class="summary-compare">
                                        <span class="compare-item">公司平均: <span id="avg-potential">xx</span>次</span>
                                        <span class="compare-item">目标要求: <span id="target-potential">xx</span>次</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 右侧柱状图 -->
                <div class="layui-col-md8">
                    <div class="chart-container">
                        <div class="chart-toolbar">
                            <div class="layui-btn-group">
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="view-total">总计视图</button>
                                <button type="button" class="layui-btn layui-btn-sm" id="view-daily">日均视图</button>
                            </div>
                        </div>
                        <div id="performance-chart" style="width: 100%; height: 350px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    #if(remark??)
    <div class="jf-panel">
        <h2 class="jf-panel-name">日常工作</h2><button class="jf-panel-more" onclick="rwbj()">编辑</button>
        <hr>
        <span class="text-wrapper">
			<span>#(remark??)</span>
		</span>
        <br>
    </div>
    #end
    <div class="jf-panel">
        <h2 class="jf-panel-name">未出货订单</h2>
        <a class="jf-panel-more" href="/my/dzhy" target="_blank">更多»</a>
        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">未填货好时间[#(ddWhhsjYsList.size()??)]</li>
                <li>已填货好时间[#(ddHhsjYsList.size()??)]</li>
            </ul>
            <div class="layui-tab-content" style="padding: 0 !important;">
                <div class="layui-tab-item layui-show">
                    <table class="layui-table" lay-size="sm">
                        <tbody>
                        #for(i:[0..9])
                        <tr>
                            #for(dd:ddWhhsjMap[i])
                            <td>
                                <a href="/my/dzhy/edit?ddbh=#(dd.ddbh??)" target="_blank">#(dd.ddbh??)</a>
                                <a href="/my/dd/edit?ddbh=#(dd.ddbh??)" target="_blank"><i class="layui-icon layui-icon-form"></i></a>
                            </td>
                            #end
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
                <div class="layui-tab-item">
                    <table class="layui-table" lay-size="sm">
                        <tbody>
                        #for(i:[0..9])
                        <tr>
                            #for(dd:ddHhsjMap[i])
                            <td>
                                <a href="/my/dzhy/edit?ddbh=#(dd.ddbh??)" target="_blank">#(dd.ddbh??)</a>
                                <a href="/my/dd/edit?ddbh=#(dd.ddbh??)" target="_blank"><i class="layui-icon layui-icon-form"></i></a>
                            </td>
                            #end
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <br>
    </div>
    #if(bqList??)
    <div class="jf-panel">
        <h2 class="jf-panel-name">便签 &nbsp;&nbsp;<a class="btn" href="/my/bq/add" target="_blank">增加</a></h2>
        <a class="jf-panel-more" href="/my/bq">更多»</a>
        <div class="layui-tab">
            <ul class="layui-tab-title">
                #for(bq:bqList)
                <li #if(for.index==(bqList.size()-1)) class="layui-this" #end>#(bq.jybt??)</li>
                #end
            </ul>
            <div class="layui-tab-content">
                #for(bq:bqList)
                <div class="layui-tab-item #if(for.index==(bqList.size()-1)) layui-show #end">
                    <h3>#(bq.bt??)</h3>
                    <p>#(bq.nr??)</p>
                </div>
                #end
            </div>
        </div>
        <br>
    </div>
    #end

    <div class="jf-panel" style="margin-top:2px;">
        <div class="layui-row">
            <div class="layui-col-xs4 layui-col-md8"><h2 class="jf-panel-name">公告:</h2></div>
        </div>
        #for(gg:ggList)
        <span class="text-wrapper clickableImg">
			<h#(gg.ztdx??6) class="h#(gg.ztdx??6)" style="color:#096;">#(gg.nr??)</h#(gg.ztdx??6)>
		</span>
        <hr>
        #end
        <h2 class="jf-panel-name" style="color:red;">累积活动资金:&nbsp;&nbsp;#(hdzjYe??0) 元 &nbsp;<a class="btn"
                                                                                                       href="/my/hdzj/add"
                                                                                                       target="_blank">更新</a>
        </h2>
        <a class="jf-panel-more" href="/my/hdzj">浏览更多»</a>
    </div>

    <div class="jf-panel">
        <h2 class="jf-panel-name"><a href="/my/sp">索赔</a></h2>
        <a class="jf-panel-more" href="/my/sp">更多»</a>
        <div class="layui-row">
            <div class="layui-col-sm2">订单ID</div>
            <div class="layui-col-sm6">索赔原因</div>
            <div class="layui-col-sm2">索赔金额</div>
            <div class="layui-col-sm2">责任方</div>
        </div>
        #for(x : spList)
        <div class="layui-row">
            <div class="layui-col-sm2">
                <h2 class="jf-my-article-title">
                    <a href="/my/sp/edit?id=#(x.id)&query=#(query??'')" target="_blank">
                        #(x.jkid??)
                    </a>
                </h2>
            </div>
            <div class="layui-col-sm6">#(x.spyy??)</div>
            <div class="layui-col-sm2">#(x.spje??) #(x.jedw??)</div>
            <div class="layui-col-sm2">#(x.zrf??)</div>
        </div>
        #end
    </div>

    <!--    <div class="jf-panel">-->
    <!--        <h2 class="jf-panel-name"><a href="javascript:getSign();">当日考勤</a></h2>-->
    <!--        <a class="jf-panel-more" href=" javascript:getSign();">刷新»</a>-->
    <!--        <div id="sign_div"></div>-->
    <!--    </div>-->

    <div class="jf-panel">
        <h2 class="jf-panel-name"><a href="/project">项目</a></h2>
        <a class="jf-panel-more" href="/project">更多»</a>
        <ul class="jf-panel-list">
            #for(x : projectList)
            <li>
                <div class="jf-panel-img">
                    <a href="/user/#(x.accountId)">
                        #if(x.avatar=="x.jpg")
                        <img class="avatar" src="/assets/img/touxiang.png">
                        #else
                        <img class="avatar" src="/upload/avatar/#(x.avatar??)">
                        #end</a>
                </div>
                <div class="jf-panel-item">
                    <h3><a href="/project/#(x.id)">#(x.title)</a></h3>
                    <p>
                        #(x.content)
                    </p>
                </div>
            </li>
            #end
        </ul>
    </div>

    <!-- 分享 -->
    <div class="jf-panel">
        <h2 class="jf-panel-name"><a href="/share">分享</a></h2>
        <a class="jf-panel-more" href="/share">更多»</a>
        <ul class="jf-panel-list">
            #for(x : shareList)
            <li>
                <div class="jf-panel-img">
                    <a href="/user/#(x.accountId)">
                        #if(x.avatar=="x.jpg")
                        <img class="avatar" src="/assets/img/touxiang.png">
                        #else
                        <img class="avatar" src="/upload/avatar/#(x.avatar??)">
                        #end</a>
                </div>
                <div class="jf-panel-item">
                    <h3>
                        <a href="/share/#(x.id)">
                            #if(x.top==1)
                            <font color="red">#(x.title)</font>
                            #else
                            #(x.title)
                            #end
                        </a>
                    </h3>
                    <p>
                        #(x.content)
                    </p>
                </div>
            </li>
            #end
        </ul>
    </div>

    <!-- 反馈 -->
    <div class="jf-panel">
        <h2 class="jf-panel-name"><a href="/feedback">反馈</a></h2>
        <a class="jf-panel-more" href="/feedback">更多»</a>
        <ul class="jf-panel-list">
            #for(x : feedbackList)
            <li>
                <div class="jf-panel-img">
                    <a href="/user/#(x.accountId)">
                        #if(x.avatar=="x.jpg")
                        <img class="avatar" src="/assets/img/touxiang.png">
                        #else
                        <img class="avatar" src="/upload/avatar/#(x.avatar??)">
                        #end</a>
                </div>
                <div class="jf-panel-item">
                    <h3><a href="/feedback/#(x.id)">#(x.title)</a></h3>
                    <p>
                        #(x.content)
                    </p>
                </div>
            </li>
            #end
        </ul>
    </div>

</div>
#end
#define css()
<style>
    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        align-items: center;
        justify-content: center;
    }

    .overlay img {
        display: block;
        margin: auto;
    }

    .clickableImg img {
        max-width: 100%;
        height: auto;
    }

    /* 业绩指标看板样式 */
    #performance-dashboard {
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    #performance-dashboard .layui-card-header h3 {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin: 0;
        padding: 5px 0;
    }

    .summary-item {
        display: flex;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed #eee;
    }

    .summary-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .summary-item i {
        font-size: 24px;
        color: #4476A7;
        margin-right: 15px;
        margin-top: 5px;
    }

    .summary-content {
        flex: 1;
    }

    .summary-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
    }

    .summary-value {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .summary-compare {
        font-size: 12px;
        color: #999;
    }

    .compare-item {
        margin-right: 15px;
    }

    .chart-container {
        background: #fff;
        border-radius: 2px;
        padding: 15px;
        height: 100%;
    }

    .chart-toolbar {
        margin-bottom: 15px;
        text-align: right;
    }

    /* 响应式调整 */
    @media screen and (max-width: 768px) {
        .layui-col-md4, .layui-col-md8 {
            width: 100%;
        }

        #performance-chart {
            height: 250px !important;
        }
    }
</style>
#end

#define js()
<script type="text/javascript" src="/assets/js/echarts.min.js"></script>
<script type="text/javascript">
    $(function () {
        var images = document.querySelectorAll('.clickableImg img');

        // 遍历所有img元素，为它们添加onclick事件处理程序
        for (var i = 0; i < images.length; i++) {
            images[i].onclick = function () {
                // 创建一个div元素来显示大图，并添加class属性和样式
                var overlay = document.createElement('div');
                overlay.classList.add('overlay');
                overlay.style.display = 'flex';

                // 创建一个img元素来显示选中的图片，设置src属性和样式
                var img = document.createElement('img');
                img.src = this.src;
                img.style.maxWidth = '100%';
                img.style.maxHeight = '100%';

                // 将img元素添加到overlay中
                overlay.appendChild(img);

                // 将overlay添加到body中
                document.body.appendChild(overlay);

                // 当overlay被点击时，删除overlay
                overlay.onclick = function () {
                    document.body.removeChild(overlay);
                };
            };
        }
    });

    $("#linkId").click(function () {
        $("#sidebar").toggleClass('layui-hide-xs');
        var tip = document.getElementById("linkId").innerText;
        document.getElementById("linkId").innerText = ("常用链接 显示" === tip) ? "常用链接 隐藏" : "常用链接 显示";
    });
    $(document).ready(function () {
    });

    function rwbj() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/account/rwbj'
        });
    }

    function jxtj() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/account/jxtj'
        });
    }

    function grjbxx() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/account/grjbxx'
        });
    }

    function pxzsgl() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/account/pxzsgl'
        });
    }

    function gwrcgz() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/rb/rbNew'
        });
    }

    function zysygh() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/account/zysygh'
        });
    }

    function grsrymbgl() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '96%'],
            fix: false,
            maxmin: false,
            content: '/my/account/grsrymbgl'
        });
    }

    function jstj() {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/account/jstj'
        });
    }

    // 业绩指标看板图表初始化
    layui.use(['element'], function(){
        var element = layui.element;

        // 模拟数据 - 实际应用中应从后端获取
        var performanceData = {
            personal: {
                coreCustomers: 45,
                contactTotal: 120,
                contactDaily: 8,
                potentialTotal: 80,
                potentialDaily: 5.3
            },
            average: {
                coreCustomers: 38,
                contactTotal: 100,
                contactDaily: 6.7,
                potentialTotal: 65,
                potentialDaily: 4.3
            },
            target: {
                coreCustomers: 50,
                contactTotal: 150,
                contactDaily: 10,
                potentialTotal: 90,
                potentialDaily: 6
            },
            dates: {
                contactStart: '2023-01-01',
                potentialStart: '2023-01-01'
            }
        };

        // 更新摘要数据
        $('#core-customer-count').text(performanceData.personal.coreCustomers);
        $('#avg-core-customer').text(performanceData.average.coreCustomers);
        $('#target-core-customer').text(performanceData.target.coreCustomers);

        $('#contact-start-date').text(performanceData.dates.contactStart);
        $('#contact-total').text(performanceData.personal.contactTotal);
        $('#contact-daily').text(performanceData.personal.contactDaily);
        $('#avg-contact').text(performanceData.average.contactTotal);
        $('#target-contact').text(performanceData.target.contactTotal);

        $('#potential-start-date').text(performanceData.dates.potentialStart);
        $('#potential-total').text(performanceData.personal.potentialTotal);
        $('#potential-daily').text(performanceData.personal.potentialDaily);
        $('#avg-potential').text(performanceData.average.potentialTotal);
        $('#target-potential').text(performanceData.target.potentialTotal);

        // 初始化ECharts实例
        var chartDom = document.getElementById('performance-chart');
        if (chartDom) {
            var myChart = echarts.init(chartDom);
            var option;

            // 默认显示总计视图
            renderTotalView();

            // 切换视图按钮事件
            $('#view-total').click(function() {
                $(this).addClass('layui-btn-normal').siblings().removeClass('layui-btn-normal');
                renderTotalView();
            });

            $('#view-daily').click(function() {
                $(this).addClass('layui-btn-normal').siblings().removeClass('layui-btn-normal');
                renderDailyView();
            });

            // 渲染总计视图
            function renderTotalView() {
                option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            var result = params[0].name + '<br/>';
                            params.forEach(function(item) {
                                result += item.marker + ' ' + item.seriesName + ': ' + item.value + '<br/>';
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['个人指标', '公司平均', '目标要求'],
                        bottom: 0
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['核心客户数量', '联系核心客户次数', '联系潜在客户次数']
                    },
                    yAxis: {
                        type: 'value',
                        name: '数量',
                        nameTextStyle: {
                            padding: [0, 0, 0, 40]
                        }
                    },
                    series: [
                        {
                            name: '个人指标',
                            type: 'bar',
                            data: [
                                performanceData.personal.coreCustomers,
                                performanceData.personal.contactTotal,
                                performanceData.personal.potentialTotal
                            ],
                            itemStyle: {
                                color: '#4476A7'
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#5588BB'
                                }
                            },
                            barMaxWidth: 50
                        },
                        {
                            name: '公司平均',
                            type: 'bar',
                            data: [
                                performanceData.average.coreCustomers,
                                performanceData.average.contactTotal,
                                performanceData.average.potentialTotal
                            ],
                            itemStyle: {
                                color: '#909399'
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#A0A0A0'
                                }
                            },
                            barMaxWidth: 50
                        },
                        {
                            name: '目标要求',
                            type: 'bar',
                            data: [
                                performanceData.target.coreCustomers,
                                performanceData.target.contactTotal,
                                performanceData.target.potentialTotal
                            ],
                            itemStyle: {
                                color: '#FF9900'
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#FFAA33'
                                }
                            },
                            barMaxWidth: 50
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 渲染日均视图
            function renderDailyView() {
                option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['个人指标', '公司平均', '目标要求'],
                        bottom: 0
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['核心客户数量', '日均联系核心客户', '日均联系潜在客户']
                    },
                    yAxis: {
                        type: 'value',
                        name: '数量',
                        nameTextStyle: {
                            padding: [0, 0, 0, 40]
                        }
                    },
                    series: [
                        {
                            name: '个人指标',
                            type: 'bar',
                            data: [
                                performanceData.personal.coreCustomers,
                                performanceData.personal.contactDaily,
                                performanceData.personal.potentialDaily
                            ],
                            itemStyle: {
                                color: '#4476A7'
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#5588BB'
                                }
                            },
                            barMaxWidth: 50
                        },
                        {
                            name: '公司平均',
                            type: 'bar',
                            data: [
                                performanceData.average.coreCustomers,
                                performanceData.average.contactDaily,
                                performanceData.average.potentialDaily
                            ],
                            itemStyle: {
                                color: '#909399'
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#A0A0A0'
                                }
                            },
                            barMaxWidth: 50
                        },
                        {
                            name: '目标要求',
                            type: 'bar',
                            data: [
                                performanceData.target.coreCustomers,
                                performanceData.target.contactDaily,
                                performanceData.target.potentialDaily
                            ],
                            itemStyle: {
                                color: '#FF9900'
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#FFAA33'
                                }
                            },
                            barMaxWidth: 50
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
    });

</script>
#end