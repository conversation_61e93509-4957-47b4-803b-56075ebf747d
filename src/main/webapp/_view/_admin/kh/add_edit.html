#@adminLayout()

#define main()
#set(isAdd = kh == null ? true : false, isEdit = !isAdd)

<div class="jfa-header-box" id="jfa-header-box">
    <div class="jfa-crumbs" id="jfa-crumbs">
        <a data-pjax class="layui-btn layui-btn-sm" href="/admin/kh"><i class="layui-icon"></i></a>客户信息 / #(isAdd ? "创建" : "编辑")
    </div>

    <div class="jfa-search-box"></div>
    #include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
    <div class="container left" id="jfa-content">
        <br>
        <form class="layui-form" action="/admin/kh/#(isAdd ? 'save' : 'update')" method="post">
            <input type="hidden" name="kh.id" value="#(kh.id??)">
            <div class="layui-form-item">
                <div class="form-inline">
                    <div class="layui-input-inline">
                        <input class="layui-btn layui-btn-sm" type="submit" value="保存"/>
                    </div>
                </div>
                #if(!isAdd)
                <div class="form-inline">
                    <div class="layui-input-inline">
                        <input class="layui-btn layui-btn-sm" type="button" onclick="showWjgl()" value="文件管理"/>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="layui-input-inline">
                        <input class="layui-btn layui-btn-sm" type="button" onclick="showHistory('#(kh.id??)', '正常订单')" value="历史订单"/>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="layui-input-inline">
                        <input class="layui-btn layui-btn-sm" type="button" onclick="showHistory('#(kh.id??)', '询价单')" value="询价订单"/>
                    </div>
                </div>
                <div class="form-inline">
                    <span>营销客户:</span>
                    <div class="layui-btn-group">
                        #for(id:yxkhList)
                        <a class="layui-btn layui-btn-sm" href="/my/yxkh/edit?id=#(id??)" target="_blank">#(kh.gsmc??)[#(for.index??+1)]</a>&nbsp;
                        #end
                    </div>
                </div>
                #end
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>简称</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.jc" name="kh.jc" value="#(kh.jc??)">
                    </div>
                </div>


                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>国别</label>
                    <div class="layui-input-inline" style="width: 90px;">
                        <input type="text" class="layui-input" id="kh.gb" placeholder="国别" name="kh.gb" list="gbList" value="#(kh.gb??)">
                        <datalist id="gbList" style="display: none">
                            <option>美国</option>
                            <option>德国</option>
                            <option>澳大利亚</option>
                            <option>英国</option>
                            <option>法国</option>
                            <option>加拿大</option>
                            <option>新西兰</option>
                            <option>俄罗斯</option>
                            <option>爱尔兰</option>
                            <option>荷兰</option>
                            <option>中国</option>
                        </datalist>
                    </div>
                    <div class="layui-input-inline" style="width: 90px;">
                        <input type="text" class="layui-input" id="kh.yy" placeholder="语言" name="kh.yy" list="yyList" value="#(kh.yy??)">
                        <datalist id="yyList" style="display: none">
                            <option>英语</option>
                            <option>德语</option>
                            <option>法语</option>
                            <option>汉语</option>
                            <option>俄语</option>
                            <option>西语</option>
                            <option>葡语</option>
                            <option>日语</option>
                            <option>韩语</option>
                            <option>阿拉伯语</option>
                        </datalist>
                    </div>
                    <div class="layui-input-inline" style="width: 90px;">
                        <input type="text" class="layui-input" id="kh.hb" placeholder="币种" name="kh.hb" list="hbList" value="#(kh.hb??)">
                        <datalist id="hbList" style="display: none">
                            <option></option>
                            <option>美元</option>
                            <option>欧元</option>
                            <option>澳元</option>
                            <option>港币</option>
                            <option>元</option>
                        </datalist>
                    </div>
                    <div class="layui-input-inline" style="width: 90px;">
                        <input type="text" class="layui-input" id="kh.zt" placeholder="状态" name="kh.zt" list="ztList" value="#(kh.zt??)">
                        <datalist id="ztList" style="display: none">
                            <option>已合作</option>
                            <option>未合作</option>
                        </datalist>
                    </div>
                    <div class="layui-input-inline" style="width: 200px;">
                        <input type="text" class="layui-input" id="kh.khpm" placeholder="产品品名" name="kh.khpm" list="khpmList" value="#(kh.khpm??)">
                        <datalist id="khpmList" style="display: none">
                            <option>GRANITE MONUMENTS</option>
                        </datalist>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>公司网址</label>
                    <div class="layui-input-inline" style="width: 930px;">
                        <input type="text" class="layui-input" id="kh.gswz" name="kh.gswz" value="#(kh.gswz??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>办公室名称</label>
                    <div class="layui-input-inline" style="width: 930px;">
                        <input type="text" class="layui-input" id="kh.gsmc" name="kh.gsmc" value="#(kh.gsmc??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>账单地址</label>
                    <div class="layui-input-inline" style="width: 930px;">
                        <input type="text" class="layui-input" id="kh.xxdz" name="kh.xxdz" value="#(kh.xxdz??)">
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>出口贸易条款</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" onkeyup="changeJgbz()" placeholder="贸易条款" id="kh.mytk" name="kh.mytk" value="#(kh.mytk??)" list="mytkList">
                        <datalist id="mytkList" style="display: none">
                            <option>FOB</option>
                            <option>DDU</option>
                            <option>DDP</option>
                            <option>CIF</option>
                        </datalist>
                    </div>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" class="layui-input" placeholder="付款方式" id="kh.fkfs" name="kh.fkfs" value="#(kh.fkfs??)" list="fkfsList">
                        <datalist id="fkfsList" style="display: none">
                            <option>货到付款</option>
                            <option>见提单付款</option>
                            <option>30%定金，剩下70%见提单复印件付款</option>
                        </datalist>
                    </div>
                </div>
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>目的港</label>
                    <div class="layui-input-inline" style="width: 270px;">
                        <input type="text" class="layui-input" placeholder="目的港" id="kh.mdg" name="kh.mdg" value="#(kh.mdg??)">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" placeholder="航期" id="kh.hq" name="kh.hq" value="#(kh.hq??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">贸易条款备注</label>
                    <div class="layui-input-inline" style="width: 800px;">
                        <input type="text" class="layui-input" placeholder="实际贸易条款备注" id="kh.mytkbz" name="kh.mytkbz" value="#(kh.mytkbz??)">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>产品价格</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" onkeyup="changeJgbz()" placeholder="公司与客户的实际贸易条款" id="kh.sjmytk" name="kh.sjmytk" value="#(kh.sjmytk??)" list="mytkList">
                    </div>
                </div>
                <div class="form-inline">
                    <label class="layui-form-label">船讯发送频率</label>
                    <div class="layui-input-inline" style="width: 600px;">
                        <div id="cxplSelect"></div>
                        <input type="hidden" id="kh.cxpl" name="kh.cxpl" value='#(kh.cxpl??)'>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">价格备注</label>
                    <div class="layui-input-inline" style="width: 930px;">
                        <input type="text" class="layui-input" placeholder="公司与客户的实际贸易条款" id="kh.jgbz" name="kh.jgbz" value="#(kh.jgbz??)" list="mytkList">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>货代</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.hd" name="kh.hd" value="#(kh.hd??)" placeholder="如:自定">
                    </div>
                </div>
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>卸货条件</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.xhtj" name="kh.xhtj" value="#(kh.xhtj??)" placeholder="如:自有叉车+人">
                    </div>
                </div>
                <div class="form-inline">
                    <label class="layui-form-label">唛头单模板</label>
                    <div class="layui-input-inline">
                        #@ss_select("kh.mtd",",SL,WTC,MSG,WDS,GET,普通",kh.mtd??)
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>DDU驾驶距离</label>
                    <div class="layui-input-inline" style="width: 520px;">
                        <input type="text" class="layui-input" placeholder="DDU或者DDP送货地址距离港口驾驶距离KM, DDU & DDP 必填" id="kh.ddujsjl" name="kh.ddujsjl" value="#(kh.ddujsjl??)">
                    </div>
                </div>
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>发单证资料</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        #@ss_select_dot("kh.fszl", ";发送;不发送", kh.fszl??, ";")
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>客户等级</label>
                    <div class="layui-input-inline" style="width: 930px;">
                        <select id="kh.dj" name="kh.dj">
                            <option></option>
                            <option value="Grade A" #if(kh.dj??=="Grade A") selected #end>Grade A: 公司的最优质客户，给公司贡献最大利益的客户，或极高信誉的好客户；</option>
                            <option value="Grade B" #if(kh.dj??=="Grade B") selected #end>Grade B: 优质客户，有基本诚信，合作愉快，有问题都能谈判化解；</option>
                            <option value="Grade C" #if(kh.dj??=="Grade C") selected #end>Grade C: 普通客户，各方面还行，合作还算过得去，偶尔脾气暴躁或难于沟通；</option>
                            <option value="Grade D" #if(kh.dj??=="Grade D") selected #end>Grade D: 一般客户，订单相对鸡肋一些，合作比较困难，有不少摩擦存在；</option>
                            <option value="Grade F" #if(kh.dj??=="Grade F") selected #end>Grade F: 垃圾客户，或者喜欢商业讹诈，不诚信，有不良记录；</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <input type="hidden" id="kh.ywy" name="kh.ywy" value="#(kh.ywy??)">
                    <label class="layui-form-label"><font color="red">*</font>跟单业务员</label>
                    <div class="layui-input-inline" style="width: 930px;">
                        <div id="ywySelect"></div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <input type="hidden" id="kh.hzywy" name="kh.hzywy" value="#(kh.hzywy??)">
                    <label class="layui-form-label"><font color="red">*</font>签约业务员</label>
                    <div class="layui-input-inline" style="width: 400px;">
                        <div id="hzywySelect"></div>
                    </div>
                </div>
                <div class="form-inline">
                    <input type="hidden" id="kh.ddgdywy" name="kh.ddgdywy" value="#(kh.ddgdywy??)">
                    <label class="layui-form-label"><font color="red">*</font>订单概况业务员</label>
                    <div class="layui-input-inline" style="width: 400px;">
                        <div id="ddgdywySelect"></div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>我司第一联系人</label>
                    <div class="layui-input-inline">
                        <select id="kh.dylxr" name="kh.dylxr">
                            <option></option>
                            <option value="<EMAIL>" #if(kh.dylxr??=="<EMAIL>") selected #end>澳林</option>
                            <option value="<EMAIL>" #if(kh.dylxr??=="<EMAIL>") selected #end>壹林</option>
                            <option value="<EMAIL>" #if(kh.dylxr??=="<EMAIL>") selected #end>菉能</option>
                        </select>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>客户第一联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khdylxrm" name="kh.khdylxrm" value="#(kh.khdylxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khdylxrfd" name="kh.khdylxrfd" value="#(kh.khdylxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khdylxr" name="kh.khdylxr" value="#(kh.khdylxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">我司第二联系人</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.delxr" name="kh.delxr" value="#(kh.delxr??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>客户第二联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khdelxrm" name="kh.khdelxrm" value="#(kh.khdelxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khdelxrfd" name="kh.khdelxrfd" value="#(kh.khdelxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khdelxr" name="kh.khdelxr" value="#(kh.khdelxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">我司第三联系人</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.dslxr" name="kh.dslxr" value="#(kh.dslxr??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>客户第三联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khdslxrm" name="kh.khdslxrm" value="#(kh.khdslxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khdslxrfd" name="kh.khdslxrfd" value="#(kh.khdslxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khdslxr" name="kh.khdslxr" value="#(kh.khdslxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>客户标签</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.bq" name="kh.bq" value="#(kh.bq??)" list="bqList">
                        <datalist id="bqList" style="display:none">
                            <option><EMAIL></option>
                            <option><EMAIL></option>
                            <option><EMAIL></option>
                        </datalist>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">客户第四联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd4lxrm" name="kh.khd4lxrm" value="#(kh.khd4lxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd4lxrfd" name="kh.khd4lxrfd" value="#(kh.khd4lxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khd4lxr" name="kh.khd4lxr" value="#(kh.khd4lxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>密度系数</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.md" name="kh.md" value="#(kh.md??)" placeholder="正常1,雕刻多小于1">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">客户第五联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd5lxrm" name="kh.khd5lxrm" value="#(kh.khd5lxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd5lxrfd" name="kh.khd5lxrfd" value="#(kh.khd5lxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khd5lxr" name="kh.khd5lxr" value="#(kh.khd5lxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">合作时间</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.hzsj" autocomplete="off" name="kh.hzsj" value="#(kh.hzsj??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">客户第六联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd6lxrm" name="kh.khd6lxrm" value="#(kh.khd6lxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd6lxrfd" name="kh.khd6lxrfd" value="#(kh.khd6lxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khd6lxr" name="kh.khd6lxr" value="#(kh.khd6lxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">开发方式</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.kffs" name="kh.kffs" list="kffsList" value="#(kh.kffs??)">
                        <datalist id="kffsList" style="display: none">
                            <option>电话</option>
                            <option>邮件</option>
                            <option>SNS</option>
                            <option>展会</option>
                            <option>拜访</option>
                            <option>介绍</option>
                            <option>询盘</option>
                        </datalist>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">客户第七联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd7lxrm" name="kh.khd7lxrm" value="#(kh.khd7lxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd7lxrfd" name="kh.khd7lxrfd" value="#(kh.khd7lxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khd7lxr" name="kh.khd7lxr" value="#(kh.khd7lxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">美金系数</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.sbmjxs" name="kh.sbmjxs" value="#(kh.sbmjxs??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">客户第八联系人</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd8lxrm" name="kh.khd8lxrm" value="#(kh.khd8lxrm??)" placeholder="名">
                    </div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" class="layui-input" id="kh.khd8lxrfd" name="kh.khd8lxrfd" value="#(kh.khd8lxrfd??)" placeholder="货号">
                    </div>
                    <div class="layui-input-inline" style="width: 385px;">
                        <input type="text" class="layui-input" id="kh.khd8lxr" name="kh.khd8lxr" value="#(kh.khd8lxr??)" placeholder="邮箱">
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>质量要求</label>
                    <div class="layui-input-inline">
                        <textarea class="layui-textarea" id="kh.bz1" name="kh.bz1">#(kh.bz1??)</textarea>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>包装要求</label>
                    <div class="layui-input-inline">
                        <textarea class="layui-textarea" id="kh.bz2" name="kh.bz2">#(kh.bz2??)</textarea>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>特殊要求</label>
                    <div class="layui-input-inline">
                        <textarea class="layui-textarea" id="kh.bz3" name="kh.bz3">#(kh.bz3??)</textarea>
                    </div>
                </div>
            </div>
            <hr>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">备注1</label>
                    <div class="layui-input-inline">
                        <textarea class="layui-textarea" id="kh.bz4" name="kh.bz4">#(kh.bz4??)</textarea>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">备注2</label>
                    <div class="layui-input-inline">
                        <textarea class="layui-textarea" id="kh.bz5" name="kh.bz5">#(kh.bz5??)</textarea>
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">客户的客户评价</label>
                    <div class="layui-input-inline">
                        <textarea class="layui-textarea" placeholder="客户的客户在Google或Yelp或Facebook上的评价" id="kh.bz6" name="kh.bz6">#(kh.bz6??)</textarea>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>客户的物流要求</label>
                    <div class="layui-input-inline" style="width: 910px;">
                        <textarea id="kh.bz7" name="kh.bz7" style="width: 100%; height: 100px;">#(kh.bz7??)</textarea>
                    </div>
                </div>
            </div>
            <hr>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>清关抬头</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.mc" name="kh.mc" value="#(kh.mc??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">代缴税</label>
                    <div class="layui-input-inline">
                        <select lay-verify="required" lay-search="" id="kh.gwdjs" name="kh.gwdjs">
                            <option value="">请选择</option>
                            <option #if(kh.gwdjs??=="NGG") selected #end>NGG</option>
                            <option #if(kh.gwdjs??=="否") selected #end>否</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width:300px;">
                        <span>优先级NGG代清关抬头>客户本身抬头</span>
                    </div>
                </div>

            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>送货地址</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.dz" name="kh.dz" value="#(kh.dz??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">NGG清关抬头</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.tdshrtt" name="kh.tdshrtt" value="#(kh.tdshrtt??)">
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>联系人</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.lxr" name="kh.lxr" value="#(kh.lxr??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">最终送货地址</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.jjdz" name="kh.jjdz" value="#(kh.jjdz??)">
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>电话</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.dh" name="kh.dh" value="#(kh.dh??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">NGG联系人</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.tdlxr" name="kh.tdlxr" value="#(kh.tdlxr??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>邮箱</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.yx" name="kh.yx" value="#(kh.yx??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">NGG电话</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.nggdh" name="kh.nggdh" value="#(kh.nggdh??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>税号</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.sh" name="kh.sh" value="#(kh.sh??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">NGG税号</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.tdsh" name="kh.tdsh" value="#(kh.tdsh??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">EORI</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.cz" name="kh.cz" value="#(kh.cz??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">清关金额系数</label>
                    <div class="layui-input-inline">
                        <input type="text" data-number class="layui-input" id="kh.qgjexs" name="kh.qgjexs" value="#(kh.qgjexs??)">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>邮编</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.yb" name="kh.yb" value="#(kh.yb??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">调整清关金额范围</label>
                    <div class="layui-input-inline" style="width: 400px;">
                        <input type="text" class="layui-input" id="kh.qgjefw" name="kh.qgjefw" placeholder="比如, 7500-8500" value="#(kh.qgjefw??)">
                    </div>
                </div>
            </div>
            <hr>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>提单通知人</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.tdtzr" name="kh.tdtzr" value="#(kh.tdtzr??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>提单抬头</label>
                    <div class="layui-input-inline w600">
                        <input type="text" class="layui-input" id="kh.tdtt" name="kh.tdtt" value="#(kh.tdtt??)" list="tdttList">
                        <datalist style="display: none" id="tdttList">
                            <option></option>
                            <option>WDS</option>
                            <option>GREEN ENERGY TECHNOLOGY</option>
                            <option>LINSTONE CO.,LTD</option>
                            <option>FUJIAN LINSTONE CO.,LTD</option>
                            <option>XIAMEN OLYMPIA STONE CO.,LTD</option>
                        </datalist>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>报关</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.bg" name="kh.bg" value="#(kh.bg??)" placeholder="如:澳林,代理">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">港杂费付款方式</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="kh.gsz" name="kh.gsz" value="#(kh.gsz??)">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label"><font color="red">*</font>清关资料</label>
                    <div class="layui-input-inline" style="width: 502px;">
                        <div id="multipleSelect"></div>
                        <input type="hidden" id="kh.qgzl" name="kh.qgzl" value="#(kh.qgzl??)">
                    </div>
                </div>

                <div class="form-inline">
                    <label class="layui-form-label">提单信息备注</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" class="layui-input" id="kh.bz" name="kh.bz" value="#(kh.bz??)">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <br>
    <hr>
</div>
<div class="layui-row layui-panel" id="wjModal" style="display: none; height: 800px;">
    <button type="button" class="layui-btn layui-btn-sm" id="documentUpload" style="margin-left: 500px;"><i class="layui-icon"></i>文档上传</button>
    <ul class="layui-box layui-box-none file-list" style="margin-left: 30px; margin-top: 20px; max-width: 600px; min-width: 300px;">
        #if(kh?? && documentFileList??)
        #for(f:documentFileList)
        <li class="layui-box layui-box-flex" style="margin-bottom: 5px;">
            <span><a class="layui-btn layui-btn-sm" href="/upload/kh/#(kh.id??)/#(f??)" target="_blank">#(f??)</a></span>
            <button class="layui-btn layui-btn-danger layui-btn-sm" onclick="deleteFile(this, '#(kh.id??)', '#(f??)')">删除</button>
        </li>
        #end
        #end
    </ul>
</div>
<style>
    .file-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>
<script type="text/javascript">
    var multipleSelect = xmSelect.render({
        el: '#multipleSelect',
        tips: '清关资料选择?',
        filterable: true,
        filterMethod: filterFn,
        initValue: '#(kh.qgzl??)'.split(/;|,|、/),
        data: [
            {name: '清单发票', value: '清单发票'},
            {name: '熏蒸单', value: '熏蒸单'},
            {name: 'FORM A产地证', value: 'FORM A产地证'},
            {name: '澳洲产地证', value: '澳洲产地证'},
            {name: '产地证', value: '产地证'},
            {name: '包装申明', value: '包装申明'},
            {name: '重量申明', value: '重量申明'}
        ],
        on: function (data) {
            var arr = data.arr;
            const values = arr.map(item => item.value);
            $('#kh\\.qgzl').val(values.join(';'));
        }
    });
    const resultArray = [];

    for (let i = 1; i <= 90; i++) {
        const item = {
            name: i.toString(),
            value: i.toString()
        };

        resultArray.push(item);
    }

    var cxplSelect = xmSelect.render({
        el: '#cxplSelect',
        tips: '请选择船讯发送日期?',
        filterable: true,
        filterMethod: filterFn,
        data: resultArray,
        initValue: $.parseJSON('#(kh.cxpl??"[]")'),
        on: function (data) {
            var arr = data.arr;
            const values = arr.map(item => item.value);
            const jsonString = JSON.stringify(values);
            console.log(jsonString);
            document.getElementById("kh.cxpl").value = jsonString;
        },
    });
    var ywySelect = xmSelect.render({
        el: '#ywySelect',
        tips: '请选择业务员?',
        filterable: true,
        filterMethod: filterFn,
        initValue: '#(kh.ywy??"")'.split(/;|,|、/),
        data: $.parseJSON('#(accountList??"[]")'),
        on: function (data) {
            var arr = data.arr;
            const values = arr.map(item => item.value);
            document.getElementById("kh.ywy").value = values.join(";");
        },
    });

    var hzywySelect = xmSelect.render({
        el: '#hzywySelect',
        tips: '请选择业务员?',
        filterable: true,
        filterMethod: filterFn,
        initValue: '#(kh.hzywy??"")'.split(/;|,|、/),
        data: $.parseJSON('#(accountList??"[]")'),
        on: function (data) {
            var arr = data.arr;
            const values = arr.map(item => item.value);
            document.getElementById("kh.hzywy").value = values.join(";");
        }
    });

    var ddgdywySelect = xmSelect.render({
        el: '#ddgdywySelect',
        tips: '请选择业务员?',
        filterable: true,
        filterMethod: filterFn,
        initValue: '#(kh.ddgdywy??"")'.split(/;|,|、/),
        data: $.parseJSON('#(accountList??"[]")'),
        on: function (data) {
            var arr = data.arr;
            const values = arr.map(item => item.value);
            document.getElementById("kh.ddgdywy").value = values.join(";");
        }
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        laydate.render({
            elem: document.getElementById('kh.hzsj')
        });
    });
    layui.upload.render({
        elem: '#documentUpload'
        , url: '/my/kh/documentUpload?id=#(kh.id??)'
        , accept: 'file'
        , multiple: true
        , timeout: 300000
        , done: function (res) {
            layer_alert(res.msg + " 可手动刷新查看。", 4000);
        }
    });

    function showWjgl() {
        layer.open({
            type: 1,
            title: "文件管理",
            area: ['50%', '300px'],
            content: $('#wjModal')
        });
    }

    function deleteFile(obj, id, fileName) {
        layer.confirm("确定要删除文件: [" + fileName + "] ?", {
            icon: 0
            , title: ''
            , shade: 0.4
            , offset: "139px"
        }, function (index) {
            $.ajax({
                url: "/my/kh/deleteFile?id=" + id + "&fileName=" + fileName,
                type: 'POST',
                cache: false,
                dataType: 'json',
                success: function (data) {
                    layer_alert(data.msg, 3000);
                    $(obj).closest("li").remove();
                }
            });
            layer.close(index);
        });
    }

    function showHistory(id, lx) {
        layer.open({
            type: 2,
            title: '历史订单',
            area: ['1000px', '600px'],
            fix: false,
            maxmin: false,
            content: '/my/dd/history?kh=' + id + '&lx=' + lx
        });
    }

    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            , beforeSerialize: function () {
                var tdtt = document.getElementById("kh.tdtt").value;
                var qgtt = document.getElementById("kh.mc").value;
                var wlyq = document.getElementById("kh.bz7").value;
                var mc = document.getElementById("kh.mc").value;
                var qgjefw = document.getElementById('kh.qgjefw').value;

                var regex = /^\d+-\d+$/;
                if (qgjefw && qgjefw!=="" && !regex.test(qgjefw)) {
                    layer_alert("清关金额范围只能放空，或者 7000-8000这种格式。");
                    document.getElementById("kh.qgjefw").focus();
                    return false;
                }
                if (mc == undefined || mc == "" || mc == null) {
                    layer_alert("清关抬头不能放空,请填写下。");
                    document.getElementById("kh.mc").focus();
                    return false;
                }
                if (tdtt == undefined || tdtt == "" || tdtt == null) {
                    layer_alert("提单抬头不能放空,请选择下。");
                    document.getElementById("kh.tdtt").focus();
                    return false;
                }
                if (qgtt == undefined || qgtt == "" || qgtt == null) {
                    layer_alert("清关抬头不能放空,请选择下。");
                    document.getElementById("kh.qgtt").focus();
                    return false;
                }
                if (wlyq == undefined || wlyq == "" || wlyq == null) {
                    layer_alert("客户的物流要求不能放空,请选择下。");
                    document.getElementById("kh.bz7").focus();
                    return false;
                }
                var xhtj = document.getElementById("kh.xhtj").value;
                if (xhtj == undefined || xhtj == "" || xhtj == null) {
                    layer_alert("卸货条件不能放空,请填写下。");
                    document.getElementById("kh.xhtj").focus();
                    return false;
                }
                var yb = document.getElementById("kh.yb").value;
                if (yb == undefined || yb == "" || yb == null) {
                    layer_alert("邮编不能放空,请填写下。");
                    document.getElementById("kh.yb").focus();
                    return false;
                }
                var europeanCountries = ["德国", "法国", "英国", "意大利", "西班牙", "荷兰", "比利时", "卢森堡", "瑞士", "奥地利", "葡萄牙", "希腊", "爱尔兰", "丹麦", "挪威", "瑞典", "芬兰", "冰岛", "波兰", "捷克", "斯洛伐克", "匈牙利", "罗马尼亚", "保加利亚", "塞浦路斯", "马耳他", "克罗地亚", "斯洛文尼亚", "爱沙尼亚", "拉脱维亚", "立陶宛", "希腊", "塞浦路斯", "马耳他", "阿尔巴尼亚", "波斯尼亚和黑塞哥维那", "黑山", "北马其顿", "塞尔维亚", "科索沃", "摩尔多瓦", "乌克兰", "白俄罗斯", "俄罗斯", "格鲁吉亚", "亚美尼亚", "阿塞拜疆", "土耳其", "格reece", "bulgaria", "croatia", "cyprus", "czech republic", "denmark", "estonia", "finland", "france", "germany", "greece", "hungary", "iceland", "ireland", "italy", "latvia", "liechtenstein", "lithuania", "luxembourg", "malta", "netherlands", "norway", "poland", "portugal", "romania", "slovakia", "slovenia", "spain", "sweden", "switzerland", "united kingdom"];

                var gbValue = document.getElementById("kh.gb").value;
                var eori = document.getElementById("kh.cz").value;

                if (europeanCountries.includes(gbValue) && (eori == undefined || eori == "" || eori == null)) {
                    layer_alert("EORI不能放空,没有就填个空格。");
                    document.getElementById("kh.cz").focus();
                    return false;
                }
                $("input[data-number]").each(function() {
                    var vaIn = $(this).val();
                    if (vaIn && !isIntNumber(vaIn) && !isFloatNumber(vaIn)) {
                        showFailMsg(vaIn + " 不能填非数字!");
                        return false;
                    }
                });
            }
            , success: function (ret) {
                layer_alert(ret.msg);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });

    function changeJgbz() {
        var mytk = document.getElementById("kh.mytk").value;
        var sjmytk = document.getElementById("kh.sjmytk").value;
        var dduRegex = /DDU/i;
        var ddpRegex = /DDP/i;
        var fobRegex = /FOB/i;
        var cifRegex = /CIF/i;
        if(cifRegex.test(mytk) && fobRegex.test(sjmytk)){
            document.getElementById("kh.jgbz").value = "创建订单时务必提前创建明细“海运费+保险”，Ocean Freight + Insurance";
        }
        if (dduRegex.test(mytk) && fobRegex.test(sjmytk)) {
            document.getElementById("kh.jgbz").value = "创建订单时务必提前创建明细“海运费+保险”、“境外清关及送货费”,Ocean Freight + Insurance,Custom Clerance + Transport Delivery";
        }
        if (ddpRegex.test(mytk) && fobRegex.test(sjmytk)) {
            document.getElementById("kh.jgbz").value = "创建订单时务必提前创建明细“海运费+保险”、“境外清关及送货费”、“代缴税费”,Ocean Freight + Insurance,Custom Clerance + Transport Delivery,Tax & Duty";
        }
    }
</script>
#end
