#@adminLayout()

#define main()

<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		<a data-pjax class="layui-btn layui-btn-sm" href="/admin/org/dd"><i class="layui-icon"></i></a>团队成员管理 增加
	</div>

	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

### 内容区域
<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">
		<form class="layui-form" id="myArticleForm" action="/admin/org/dd_save" method="post">

			<div class="layui-form-item">
				<label class="layui-form-label">机构名称</label>
				 <div class="layui-input-inline">
					<select lay-verify="required" lay-search="" name="jgid">
							<option value="">请选择</option>
						#for( x : jgList)
							<option value="#(x.id)">#(x.mc??)</option>
						#end
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">部门名称</label>
				 <div class="layui-input-inline">
					<select lay-verify="required" lay-search="" name="bmid">
						<option value="">请选择</option>
						#for( x : bmList)
						<option value="#(x.id)">#(x.mc??)</option>
						#end
					</select>
				</div>
			</div>


			<div class="layui-form-item">
				<label class="layui-form-label">成员名称</label>
				 <div class="layui-input-inline">
					<select lay-verify="required" lay-search="" name="cyid">
						<option value="">请选择</option>
						#for( x : cyList)
						<option value="#(x.id)">#(x.mc??)</option>
						#end
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				 <div class="layui-input-inline">
					<input class="layui-btn layui-btn-sm" type="submit" value="提交" />
				</div>
			</div>
		</form>
	</div><!-- END OF jfa-content -->
</div><!-- END OF jfa-content-box --><script type="text/javascript">
	$(document).ready(function() {
		$("#myArticleForm").ajaxForm({
			dataType: "json"
			, beforeSubmit: function(formData, jqForm, options) {}
			, success: function(ret) {
				layer_alert_with_callback(ret.msg, ret.state, "/admin/org/dteam?p=#(p??1)");
			}
			, error: function(ret) {layer_alert(ret.msg);}
			, complete: function(ret) {} 	      // 无论是 success 还是 error，最终都会被回调
		});
	});
</script>

#end
