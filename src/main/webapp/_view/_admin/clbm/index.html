#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		材料别名
	</div>
	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">
		<div class="pull-right">
			<form class="layui-form" action="/admin/clbm/importFile" method="post" enctype="multipart/form-data">
				<input class="layui-input" type="file" name="file_name">
				<button class="layui-btn layui-btn-sm" type="submit">导入</button>
			</form>
		</div>
		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/admin/clbm/add">
				<i class="fa fa-plus"></i>
				添加
			</a>
			<a class="layui-btn layui-btn-sm" data-pjax href="/download/石材别名导入.xls">模板下载</a>
		</div>

		<div class="jfa-table-box">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th>ID</th>
					<th>名称</th>
					<th>对应石种ID</th>
					<th>对应石种名称</th>

					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : page.getList())
				<tr>
					<th scope="row">#(x.id)</th>
					<td>#(x.mc??)</td>
					<td>#(x.clid??)</td>
					<td>#(x.clmc??)</td>

					<td class="jfa-operation-button">
						<a data-pjax href="/admin/clbm/edit?id=#(x.id)&p=#(page.pageNumber)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>
						<a data-delete
						   data-title="#escape(x.mc)"
						   data-action="/admin/clbm/delete?id=#(x.id)">
							<i class="fa fa-trash" title="删除"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>
			<div>
				#@adminPaginate(page.pageNumber, page.totalPage, "/admin/clbm?p=")
			</div>
		</div>

	</div>
</div>

#end

