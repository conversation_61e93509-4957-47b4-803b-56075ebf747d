#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		基础数据管理
	</div>
	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

### 内容区域
<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">

		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/admin/base/add">
				<i class="fa fa-plus"></i>
				添加基础数据
			</a>
		</div>

		<div class="jfa-table-box">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th style="width: 100px;">id</th>
					<th style="width: 200px;">名称</th>
					<th style="width: 300px;">数值</th>
					<th style="width: 100px;">默认值</th>
					<th>数值说明</th>
					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : jcsjPage.getList())
				<tr>
					<th scope="row" onclick="location='/admin/base/edit?uid=#(x.uid)&p=#(jcsjPage.pageNumber)'">#(x.id)</th>
					<td onclick="location='/admin/base/edit?uid=#(x.uid)&p=#(jcsjPage.pageNumber)'">#(x.mc)</td>
					<td onclick="location='/admin/base/edit?uid=#(x.uid)&p=#(jcsjPage.pageNumber)'">#(x.sz)</td>
					<td onclick="location='/admin/base/edit?uid=#(x.uid)&p=#(jcsjPage.pageNumber)'">#(x.mrz)</td>
					<td onclick="location='/admin/base/edit?uid=#(x.uid)&p=#(jcsjPage.pageNumber)'">#(x.szsm)</td>
					<td class="jfa-operation-button">
						<a data-pjax href="/admin/base/edit?uid=#(x.uid)&p=#(jcsjPage.pageNumber)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>
						<a data-delete
						   data-title="#escape(x.mc)"
						   data-action="/admin/base/delete?uid=#(x.uid)">
							<i class="fa fa-trash" title="删除"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>

			<div>
				#@adminPaginate(jcsjPage.pageNumber, jcsjPage.totalPage, "/admin/base?p=")
			</div>
		</div>

	</div><!-- END OF jfa-content -->
</div><!-- END OF jfa-content-box -->

<script type="text/javascript">
</script>
#end

