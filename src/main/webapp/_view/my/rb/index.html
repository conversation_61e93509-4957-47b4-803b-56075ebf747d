#set(seoTitle="日报管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li class="active">日报</li>
        </ol>
        <form method="post" action="/my/rb">
            <input type="text" name="query" placeholder="请输入搜索内容" size="20">
            <input class="layui-btn layui-btn-sm" type="submit" value="查找">
        </form>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/rb/add">创&nbsp;&nbsp;建</a>
        </div>
    </div>
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th style="width: 100px;">日期</th>
            <th style="width: 200px;">记录人</th>
            <th style="width: 400px;">内容</th>
            <th style="width: 400px;">评语</th>
            <th style="width: 160px;">文件</th>
            <th style="width: 100px;">操作</th>
        </tr>
        </thead>
        <tbody>
        #for(x : page.list)
        <tr>
            <td>#(x.jlrq??)</td>
            <td>#(x.username??)</td>
            <td>新增: 产品 #(x.xzcp??0)
                ，询价 #(x.xzxj??0)
                ，客户新建 #(x.xzyxkh??0)
                ，客户背调 #(x.xzbd??0)
                ，客户沟通 #(x.xzgt??0)
                ，订单维护 #(x.xzddgj??0)
                ，每日学习 #(x.xzxx??0)
            </td>
            <td>#(x.py_hh??)</td>
            <td>
                #if(x.wj??)
                #set(wjAttr=(x.wj).split(";"))
                #for(wj : wjAttr)
                <a href="/upload/rb/#(wj??)" title="#(wj??)" target="_blank">#(wj??)</a>&nbsp;
                #end
                #end
            </td>
            <td>
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-sm" href="/my/rb/edit?id=#(x.id)" target="_blank">编辑</a>
    <!--                <a href="javascript:void(0);" onclick="sendClick('#(x.id??)')">发送</a>-->
                    #role("权限管理员", "超级管理员", "总经理", "WDS管理员")
                    <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.username??) 后无法恢复，确定要删除？', '/my/rb/delete?id=#(x.id)', this);">删除</span>
                    #end
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/my/rb?query=" + query + "&p=")
</div>

<div id="defaultModal" style="display: none; width: 800px; height: 600px;">
    <form id="sendForm" class="layui-form" method="post" action="/my/rb/send">
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">发送对象</label>
                <div class="layui-input-block">
                    <input type="hidden" name="sendId" id="sendId">
                    <input type="hidden" name="emails" id="emails">
                    <div id="multipleSelect"></div>
                </div>
            </div>
            <br>
            <div class="form-inline">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn layui-btn-sm">发送</button>
                </div>
            </div>
        </div>
    </form>
</div>
#end
#end

#define js()
<script type="text/javascript">
    var multipleSelect = xmSelect.render({
        el: '#multipleSelect',
        tips: '请选择要发送的同事?',
        filterable: true,
        filterMethod: filterFn,
        data: [
            {name: '阿海: <EMAIL>', value: '<EMAIL>'},
            {name: '阿秋: <EMAIL>', value: '<EMAIL>'},
            {name: '老柳: <EMAIL>', value: '<EMAIL>'},
            {name: '阿旭: <EMAIL>', value: '<EMAIL>'},
        ]
    });

    function sendClick(v) {
        document.getElementById('sendId').value = v;
        layer.open({
            type: 1,
            title: "附件",
            content: $('#defaultModal'),
            done: function (){
                multipleSelect.render();
            },
            end: function (){
                document.getElementById("defaultModal").style.display="none";
            }
        });
    }

    $(document).ready(function () {
        $("#sendForm").ajaxForm({
            dataType: "json"
            , beforeSerialize: function () {
                document.getElementById("emails").value = multipleSelect.getValue("valueStr").replace(/,/g, ";");
            }
            , success: function (ret) {
                if (ret.state === "ok") {
                    layer_alert("发送成功!");
                    layer.closeAll();
                    document.getElementById("defaultModal").style.display="none";
                } else {
                    layer_alert(ret.msg);
                    layer.closeAll();
                    document.getElementById("defaultModal").style.display="none";
                }
            }
            , error: function (ret) {
                layer_alert(ret.msg);
                layer.closeAll();
                document.getElementById("defaultModal").style.display="none";
            }
        });
    });
</script>
#end