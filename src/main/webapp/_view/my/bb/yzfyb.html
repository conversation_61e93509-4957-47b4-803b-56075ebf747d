#set(seoTitle="运杂费月报")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="aolinReportModule" class="clickable-menu-item"><i class="layui-icon layui-icon-chart"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('澳林报表')">澳林报表</button></li>
            <li class="active">运杂费月报</li>
        </ol>
        <div class="layui-row">
             <div class="layui-input-inline">
                <form method="post" action="/my/bb/yzfyb">
                    <input type="text" id="ksyf" name="ksyf" value="#(ksyf??)" placeholder="请输入开始月份" size="20">
                    <input type="text" id="jsyf" name="jsyf" value="#(jsyf??)" placeholder="请输入结束月份" size="20">
                    <input class="layui-btn layui-btn-sm" type="submit" value="查找">
                    <a href="#" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});"> <img src="/assets/img/xls.png" alt="XLSX" style="width:24px"></a>
                </form>
            </div>
        </div>
    </div>

    <br>
    <table id="myTable01"  class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th>月份</th>
            <th>运杂费</th>
        </tr>
        </thead>
        <tbody>
        #for(record : records??)
        <tr>
            <td>#(record.cq??'合计')</td>
            <td>#(record.yzf??)</td>
        </tr>
        #end
        </tbody>
    </table>
</div>
#end
#end

#define js()
    <script type="application/javascript" src="/assets/export/js-xlsx/xlsx.core.min.js"></script>
    <script type="application/javascript" src="/assets/export/tableExport.min.js"></script>
    <script type="text/javascript">
        layui.use('laydate', function() {
            var laydate = layui.laydate;

            laydate.render({
                elem: '#ksyf'
                ,format: 'yyyy-MM'
            });
            laydate.render({
                elem: '#jsyf'
                ,format: 'yyyy-MM'
            });
        });
        function doExport(selector, params) {
            var options = {
                fileName: '运杂费汇总'
            };

            jQuery.extend(true, options, params);

            $(selector).tableExport(options);
        }
    </script>
#end