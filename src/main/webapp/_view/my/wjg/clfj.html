#set(seoTitle="造型外加工管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

    #define content()
	<div>
		<div class="jf-breadcrumb-box">
			<ol class="jf-breadcrumb">
				<li><a href="/">首页</a></li>
				<li id="financeModule" class="clickable-menu-item"><i class="layui-icon layui-icon-rmb"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('财务模块')">财务模块</button></li>
				<li class="active"><a href="/my/wjg" class="layui-btn layui-btn-sm">外加工和喷砂</a></li>
				<li><a href="/my/wjg/index_qt?table=wjg_dm" class="layui-btn layui-btn-sm">大磨</a></li>
				<li><a href="/my/wjg/index_qt?table=wjg_rm" class="layui-btn layui-btn-sm">软磨</a></li>
				<li><a href="/my/wjg/index_qt?table=wjg_zx" class="layui-btn layui-btn-sm">造型</a></li>
				<li><a href="/my/wjg/clfj" class="layui-btn layui-btn-sm layui-btn-danger">材料附加</a></li>
				<li><a href="/my/wjg/zhcx" class="layui-btn layui-btn-sm">综合查询</a></li>
			</ol>
		</div>
		<br>
		<form class="layui-form" method="post" action="/my/wjg/clfj">
			<div class="layui-row layui-col-space10 layui-form-item">
				<div class="layui-col-lg1"><input class="layui-input" type="text" value="#(query??)" name="query" placeholder="输入订单查询"></div>
				<div class="layui-col-lg3"><button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
					<a href="#" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});"> <img src="/assets/img/xls.png" alt="XLSX" style="width:24px"></a>
				</div>
			</div>
		</form>
		<table  class="layui-table" lay-size="sm" id="myTable01">
			<thead>
				<tr>
					<th>订单编号</th>
					<th>工程编号</th>
					<th>中文石种</th>
					<th>Sizea CM</th>
					<th>Siceb CM</th>
					<th>Sicec CM</th>
					<th>最小尺寸CM</th>
					<th>售价(含附加)</th>
					<th>材料总价</th>
					<th>附加系数</th>
					<th>材料附加</th>
				</tr>
			</thead>
			<tbody>
			#for(x : clfjList)
				<tr>
					<td><a href="/my/dd/edit?ddbh=#(x.ddbh??)" class="layui-btn layui-btn-sm" target="_blank">#(x.ddbh??)</a></td>
					<td>#(x.gcdd??)</td>
					<td>#(x.zwszm??)</td>
					<td>#(x.sizeacm??)</td>
					<td>#(x.sizebcm??)</td>
					<td>#(x.sizeccm??)</td>
					<td>#(x.zxcc??)</td>
					<td>#(x.mhzj??)</td>
					<td>#(x.clzj??)</td>
					<td>#(x.xs??)</td>
					<td>#(x.clfj??)</td>
				</tr>
			#end
			<tr>
				<td><b>合计</b></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td>#(clfjhj??)</td>
			</tr>
			</tbody>
		</table>
	</div>
	#end
#end


#define js()
<script type="application/javascript" src="/assets/export/js-xlsx/xlsx.core.min.js"></script>
<script type="application/javascript" src="/assets/export/tableExport.min.js"></script>

<script type="text/javascript">
	function doExport(selector, params) {
		var options = {
			fileName: '材料附加'
		};
		jQuery.extend(true, options, params);
		$(selector).tableExport(options);
	}
</script>
#end