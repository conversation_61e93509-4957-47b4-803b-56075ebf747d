#set(seoTitle="WDS 账户编辑")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="layui-panel">
    <div class="jfa-content" id="jfa-content">

        <div class="layui-form-item">
            <label class="layui-form-label">激活</label>
            <div class="layui-input-inline">
                <input data-id="#(account.id)" #if(account.isStatusOk()) checked #end type="checkbox" class="mgc-switch mgc-tiny">
            </div>
        </div>

        <form class="layui-form" id="myArticleForm" action="/my/account/update" method="post">
            <input type="hidden" name="account.id" value="#(account.id)"/>

            <div class="layui-form-item">
                <label class="layui-form-label">头像</label>
                 <div class="layui-input-inline">
                    <img src="/upload/avatar/#(account.avatar??)">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">昵称</label>
                 <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="account.nickName" value="#(account.nickName??)">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="account.xm" value="#(account.xm??)">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">手机</label>
                 <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="account.sj" value="#(account.sj??)">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">微信</label>
                 <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="account.wx" value="#(account.wx??)">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">邮箱</label>
                 <div class="layui-input-inline">
                    <input type="email" class="layui-input" name="account.userName" value="#(account.userName)" placeholder="填写合法的 email">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">标签[WDS]</label>
                <div class="layui-input-inline">
                    #@ss_select("account.bq", "WDS,WDS离职", account.bq??)
                </div>
            </div>
        </form>

    </div><!-- END OF jfa-content -->
</div><!-- END OF jfa-content-box -->

<style type="text/css">
    #myArticleForm {
        width: 500px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .jfa-content label {
        line-height: 1;
        vertical-align: baseline;
        color: #23527c;
        font-size: 20px;
        font-weight: normal;
        margin-bottom: 8px;;
    }

    .change-avatar {
        text-decoration: underline;
        display: inline-block;
        margin: 0 0 0 25px;
        vertical-align: bottom;
        font-size: 16px;
    }
</style>

<script type="text/javascript">
    $(document).ready(function () {
        $("#myArticleForm").ajaxForm({
            dataType: "json"
            , beforeSubmit: function (formData, jqForm, options) {
            }
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/account/edit?id="+ret.id);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
            , complete: function (ret) {
            } 	      // 无论是 success 还是 error，最终都会被回调
        });
    });
</script>

<script type="text/javascript">
    $(document).ready(function () {
        initMagicInput(prepareAction);
    });

    function prepareAction($this, state) {
        return {
            url: state ? "/my/account/active" : "/my/account/unactive",
            data: {
                id: $this.attr("data-id")
            }
        }
    }
</script>

#end
#end
