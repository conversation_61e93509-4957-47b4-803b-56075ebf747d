#set(seoTitle="加工备注管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="jf-breadcrumb-box">
    <ol class="jf-breadcrumb">
        <li><a href="/">首页</a></li>
        <li id="resourceModule" class="clickable-menu-item"><i class="layui-icon layui-icon-app"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('资源模块')">资源模块</button></li>
        <li class="active">加工备注</li>
    </ol>
    <div class="jf-btn-box">
        <a class="layui-btn layui-btn-sm" href="/my/jgbz/add">创&nbsp;&nbsp;建</a>
    </div>
</div>
<br>
<form class="layui-form" method="post" action="/my/jgbz">
    <div class="layui-form-item">
        <div class="form-inline">
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="query" id="query" value="#(query??)" placeholder="输入内容查询">
            </div>
            <div class="layui-input-inline">
                #@fy_select()
            </div>
            <div class="layui-input-inline" style="width: 400px;">
                <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
                <a class="layui-btn layui-btn-sm" href="/my/jgbz/export" target="_blank">导出</a>
            </div>
        </div>
    </div>
</form>
<h4>英文，德文名称直接填写，自动保存</h4>
<table class="layui-table" lay-size="sm">
    <thead>
    <tr>
        <th>中文名称</th>
        <th>英文名称</th>
        <th>德文名称</th>
        <th style="width: 135px;">操作</th>
    </tr>
    </thead>
    <tbody>
    #for(x : page.list)
    <tr>
        <td>#(x.zwmc??)</td>
        <td><input class="layui-input" style="background-color: lightblue" value="#(x.ywmc??)" onkeyup="changeYwmc('#(x.id??)', this.value)"></td>
        <td><input class="layui-input" style="background-color: lightblue" value="#(x.dwmc??)" onkeyup="changeDwmc('#(x.id??)', this.value)"></td>

        <td>
            <div class="layui-btn-group">
                <a class="layui-btn layui-btn-sm" href="/my/jgbz/edit?id=#(x.id)">编辑</a>&nbsp;&nbsp;
                #role("权限管理员", "超级管理员", "总经理")
                <button class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.id??) 后无法恢复，确定要删除？', '/my/jgbz/delete?id=#(x.id)', this);">删除</button>
                #end
            </div>
        </td>
    </tr>
    #end
    </tbody>
</table>
#@paginate(page.pageNumber, page.totalPage, "/my/jgbz?query=" + query + "&queryKsrq=" + queryKsrq + "&queryJsrq=" + queryJsrq + "&queryPx=" + queryPx + "&pageSize" + pageSize + "&p=")
#end
#end


#define js()
<script type="application/javascript">
    function changeDwmc(id, dwmc) {
        $.ajax({
            url: "/my/jgbz/updateDwmc",
            async: true,
            type: "POST",
            cache: false,
            data: {
                id: id,
                dwmc: dwmc
            },
            success: function (data) {
                console.log(data.msg);
            },
            error: function (data) {
                layer_alert(data.msg);
            },
            dataType: "json"
        });
    }
    function changeYwmc(id, ywmc) {
        $.ajax({
            url: "/my/jgbz/updateYwmc",
            async: true,
            type: "POST",
            cache: false,
            data: {
                id: id,
                ywmc: ywmc
            },
            success: function (data) {
                console.log(data.msg);
            },
            error: function (data) {
                layer_alert(data.msg);
            },
            dataType: "json"
        });
    }
</script>
#end