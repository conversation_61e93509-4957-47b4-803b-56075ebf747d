#set(seoTitle="客户维护管理 批量 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/zdhf">客户维护</a></li>
            <li class="active">#(isAdd ? "批量创建客户维护" : "批量编辑客户维护")</li>
        </ol>
    </div>
    <br>
    <br>
    <form class="layui-form" style="white-space:nowrap;" action="/my/zdhf/next" method="post" onsubmit="return submitForm();">
        <input type="hidden" name="zdhf.id" value="#(zdhf.id??)">
        <input type="hidden" name="zdhf.lx" value="#(zdhf.lx??)">

        <div class="layui-form-item">
            <label class="layui-form-label">客户</label>
            <div class="layui-input-block">
                <select id="zdhf.khid" name="zdhf.khid" lay-verify="required" lay-search lay-filter="khSelect" title="请选择客户">
                    <option></option>
                    #for(kh : khList)
                    <option value="#(kh.id??)" #if((kh.id).toString()??==zdhf.khid??) selected #end>#(kh.jc??)</option>
                    #end
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货号/分店</label>
            <div class="layui-input-block">
                <div id="hhSelect"></div>
                <input type="hidden" class="layui-input" id="zdhf.khhh" name="zdhf.khhh" value="#(zdhf.khhh??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货柜编号</label>
            <div class="layui-input-block">
                <div id="hgSelect"></div>
                <input type="hidden" class="layui-input" id="zdhf.ddbh" name="zdhf.ddbh" value="#(zdhf.ddbh??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">邮件</label>
            <div class="layui-input-block">
                #@select("zdhf.yjid","yj","id","mc"," and fl='固定邮件' and zt<>'禁用' order by px+0 ",zdhf.yjid??)
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附加邮件标题</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="zdhf.bt" name="zdhf.bt" value="#(zdhf.bt??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea class="layui-textarea" cols="60" rows="15" id="zdhf.bz" name="zdhf.bz">#(zdhf.bz??)</textarea>
            </div>
        </div>

        <div class="layui-col-sm12">
             <div class="layui-input-inline">
                <input class="layui-btn layui-btn-sm" type="submit" value="下一步"/>
            </div>
        </div>
    </form>
</div>
<br>
#end
#end

#define js()
<script type="text/javascript">
    var hhSelectJson = $.parseJSON('#(hhSelectJson??"[]")');
    var ddSelectJson = $.parseJSON('#(ddSelectJson??"[]")');
    var hhSelect = xmSelect.render({
        el: '#hhSelect',
        tips: '请选择客户分店/货号?',
        toolbar: {show: true},
        filterable: true,
        filterMethod: filterFn,
        data: hhSelectJson
    });
    var hgSelect = xmSelect.render({
        el: '#hgSelect',
        tips: '请选择货柜号?',
        toolbar: {show: true},
        filterable: true,
        filterMethod: filterFn,
        data: ddSelectJson
    });
    layui.form.on("select(khSelect)", function () {
        $.ajax({
            url: '/my/zdhf/getDdList',
            type: 'POST',
            data: {
                "kh": $("#zdhf\\.khid").val()
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                var updateData = (data == undefined) ? [] : data;
                hgSelect.update({
                    data: updateData
                });
                hgSelect.setValue([]);
            }
        });
        $.ajax({
            url: '/my/zdhf/getKhhhList',
            type: 'POST',
            data: {
                "kh": $("#zdhf\\.khid").val()
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                var updateData = (data == undefined) ? [] : data;
                hhSelect.update({
                    data: updateData
                });
                hhSelect.setValue([]);
            }
        });
    });
    function submitForm(){
        document.getElementById("zdhf.khhh").value = hhSelect.getValue("valueStr");
        document.getElementById("zdhf.ddbh").value = hgSelect.getValue("valueStr").replace(/,/g, ";");
        return true;
    }
</script>
#end
