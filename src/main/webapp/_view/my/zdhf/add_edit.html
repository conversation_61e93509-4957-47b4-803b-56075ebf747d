#set(seoTitle="客户维护管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/zdhf">客户维护</a></li>
            <li class="active">#(isAdd ? "创建客户维护" : "编辑客户维护")</li>
        </ol>
    </div>
    <br>
    <br>
    <form class="layui-form" style="white-space:nowrap;" enctype="multipart/form-data" action="/my/zdhf/#(isAdd ? 'save' : 'update')" method="post">
        <input type="hidden" name="zdhf.id" value="#(zdhf.id??)">
        <input type="hidden" name="zdhf.lx" value="#(zdhf.lx??)">

        <div class="layui-form-item">
            <div class="layui-input-inline" style="margin-left: 50px;">
                <input class="layui-btn layui-btn-sm" type="submit" value="保存"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户</label>
            <div class="layui-input-inline">
                <select id="zdhf.khid" name="zdhf.khid" lay-verify="required" lay-search lay-filter="khSelect" title="请选择客户">
                    <option></option>
                    #for(kh : khList)
                    <option value="#(kh.id??)" #if((kh.id).toString()??==zdhf.khid??) selected #end>#(kh.jc??)</option>
                    #end
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货号/分店</label>
            <div class="layui-input-inline" style="width: 600px;">
                <div id="hhSelect"></div>
                <input type="hidden" class="layui-input" id="zdhf.khhh" name="zdhf.khhh" value="#(zdhf.khhh??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货柜编号</label>
            <div class="layui-input-inline" style="width: 600px;">
                <div id="hgSelect"></div>
                <input type="hidden" class="layui-input" id="zdhf.ddbh" name="zdhf.ddbh" value="#(zdhf.ddbh??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">工程编号</label>
            <div class="layui-input-inline" style="width: 600px;">
                <div id="gcSelect"></div>
                <input type="hidden" class="layui-input" id="zdhf.gcbh" name="zdhf.gcbh" value="#(zdhf.gcbh??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">发送表格</label>
            <div class="layui-input-inline" style="width: 300px;">
                <input type="hidden" id="zdhf.fsbg" name="zdhf.fsbg" value="#(zdhf.fsbg??'0')">
                <input type="checkbox" id="fsbgCheckBox" title="是否发送" #if(eq2Obj(zdhf.fsbg??, 1)) checked #end>
                <a class="layui-btn layui-btn-sm" href="javascript:void(0);" onclick="showTable()">查看</a>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">邮件</label>
            <div class="layui-input-inline" style="width: 400px;">#@select("zdhf.yjid","yj","id","mc"," and fl='固定邮件' and zt<>'禁用' order by px+0 ",zdhf.yjid??)</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附加邮件标题</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="zdhf.bt" name="zdhf.bt" value="#(zdhf.bt??)">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">价格</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="zdhf.jg" name="zdhf.jg" value="#(zdhf.jg??)">
            </div>
            <div class="layui-input-inline" style="width: 100px;">
                #@simple_select("zdhf.hb", "USD,EUR,AUD,RMB", zdhf.hb??, "")
            </div>
        </div>
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">中文石种</label>
                <div class="layui-input-inline">
                    #@select("zdhf.zwsz","cl","id","mc"," ",zdhf.zwsz??)
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">中文加工</label>
                <div class="layui-input-inline">#@select("zdhf.zwjg","jgfs","id","mc"," ",zdhf.zwjg??)</div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">审核状态</label>
                <div class="layui-input-inline">
                    #role("权限管理员", "超级管理员", "总经理")
                    #@simple_select("zdhf.shzt", "创建,通过", zdhf.shzt??, "创建")
                    #end
                    #norole("权限管理员", "超级管理员", "总经理")
                    #(zdhf.shzt??)
                    #end
                </div>
            </div>
            <input type="hidden" name="zdhf.fszt" value="#(zdhf.fszt??)">
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附件</label>
            <div class="layui-input-inline">
                <input type="hidden" id="zdhf.fj" name="zdhf.fj" value="#(zdhf.fj??)">
                <input type="file" multiple name="wj">
            </div>
            #for(wj : wjList??)
            <div class="layui-input-inline" style="width: 400px">
                <a href="/upload/zdhf/#(zdhf.id??)/#(wj??)" title="#(wj??)" target="_blank">#(wj??)</a>&nbsp;
                <a href="javascript:if(confirm('确认删除？')) location.href='/my/zdhf/deleteFile?id=#(zdhf.id??)&wj=#(wj??)'">删除</a>&nbsp;&nbsp;
            </div>
            #end
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-inline" style="width: 586px;">
                <textarea class="layui-textarea" style="width: 98%; height: 100px;" id="zdhf.bz" name="zdhf.bz">#(zdhf.bz??)</textarea>
            </div>
        </div>

        #if(!isAdd)
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">创建人</label>
                <div class="layui-input-inline">#(zdhf.cjr??)</div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">创建时间</label>
                <div class="layui-input-inline">#(zdhf.cjsj??)</div>
            </div>
        </div>
        #end
    </form>
</div>
<br>
#end
#end

#define js()
<script type="text/javascript">
    var hhSelectJson = $.parseJSON('#(hhSelectJson??"[]")');
    var ddSelectJson = $.parseJSON('#(ddSelectJson??"[]")');
    var gcSelectJson = $.parseJSON('#(gcSelectJson??"[]")');
    var hhSelect = xmSelect.render({
        el: '#hhSelect',
        tips: '请选择客户分店/货号?',
        toolbar: {show: true},
        filterable: true,
        filterMethod: filterFn,
        data: hhSelectJson
    });
    var hgSelect = xmSelect.render({
        el: '#hgSelect',
        tips: '请选择货柜号?',
        toolbar: {show: true},
        filterable: true,
        filterMethod: filterFn,
        data: ddSelectJson,
        on: function (data) {
            var arr = data.arr;
            var ddbh = "";
            for (var i = 0; i < arr.length; i++) {
                var d = arr[i];
                ddbh += d.value + ";";
            }
            console.log(ddbh);
            $.ajax({
                url: '/my/zdhf/getGcbhList',
                type: 'POST',
                data: {
                    "ddbh": ddbh
                },
                cache: false,
                dataType: 'json',
                success: function (data) {
                    var updateData = (data == undefined) ? [] : data;
                    gcSelect.update({
                        data: updateData
                    });
                    gcSelect.setValue([]);
                }
            });
        },
    });
    var gcSelect = xmSelect.render({
        el: '#gcSelect',
        tips: '必选，请选择订单工程?没有可以在搜索框填个日期，如:20231026，会生成一个可选项',
        filterable: true,
        filterMethod: filterFn,
        toolbar: {show: true},
        data: gcSelectJson,
        create: function (val, arr) {
            return {
                name: val,
                value: val
            }
        }
    });


    layui.form.on("select(khSelect)", function () {
        $.ajax({
            url: '/my/zdhf/getDdList',
            type: 'POST',
            data: {
                "kh": $("#zdhf\\.khid").val()
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                var updateData = (data == undefined) ? [] : data;
                hgSelect.update({
                    data: updateData
                });
                hgSelect.setValue([]);
            }
        });
        $.ajax({
            url: '/my/zdhf/getKhhhList',
            type: 'POST',
            data: {
                "kh": $("#zdhf\\.khid").val()
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                var updateData = (data == undefined) ? [] : data;
                hhSelect.update({
                    data: updateData
                });
                hhSelect.setValue([]);
            }
        });
        $.ajax({
            url: '/my/zdhf/getHb',
            type: 'POST',
            data: {
                "kh": $("#zdhf\\.khid").val()
            },
            cache: false,
            dataType: 'html',
            success: function (data) {
                $("#zdhf\\.hb").val(data);
                layui.form.render("select");
            }
        });
    });

    $(document).ready(function () {
        var hhArray = '#(zdhf.khhh??"")'.split(/,/);
        hhSelect.setValue(hhArray);
        var hgArray = '#(zdhf.ddbh??"")'.split(/;|,|、/);
        hgSelect.setValue(hgArray);
        var gcArray = '#(zdhf.gcbh??"")'.split(/;|,|、/);
        gcSelect.setValue(gcArray);

        $("form").ajaxForm({
            dataType: "json"
            , beforeSubmit: function () {
                let value = document.getElementById("zdhf.yjid").value;
                if (value == undefined || value == null || value == "") {
                    layer_alert("邮件主题必须选");
                    document.getElementById("zdhf.yjid").focus();
                    return false;
                }
                let gcbhVal = gcSelect.getValue("valueStr");
                if (gcbhVal == undefined || gcbhVal == null || gcbhVal == "") {
                    layer_alert("工程编号必须选");
                    document.getElementById("gcSelect").focus();
                    return false;
                }
                return true;
            }
            , beforeSerialize: function () {
                document.getElementById("zdhf.khhh").value = hhSelect.getValue("valueStr");
                document.getElementById("zdhf.ddbh").value = hgSelect.getValue("valueStr").replace(/,/g, ";");
                document.getElementById("zdhf.gcbh").value = gcSelect.getValue("valueStr").replace(/,/g, ";");
                if (document.getElementById('fsbgCheckBox').checked) {
                    document.getElementById('zdhf.fsbg').value = 1;
                } else {
                    document.getElementById('zdhf.fsbg').value = 0;
                }
            }
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/zdhf/edit?id="+ret.id);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });

    function showTable() {
        let ddbh = hgSelect.getValue("valueStr").replace(/,/g, ";");
        let gcdd = gcSelect.getValue("valueStr").replace(/,/g, ";");
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/zdhf/showTable?ddbh=' + ddbh + '&gcdd=' + gcdd
        });
    }
</script>
#end
