#set(seoTitle="快递费管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/kdf">澳林快递费</a></li>
            <li class="active">#(isAdd ? "创建" : "编辑")</li>
        </ol>
    </div>
    <br>
    <br>
    <form class="layui-form" action="/my/kdf/#(isAdd ? 'save' : 'update')" method="post">
        <input type="hidden" name="kdf.id" value="#(kdf.id??)">

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">日期</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.rq" name="kdf.rq" value="#(kdf.rq??)" autocomplete="off">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">货物</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.hw" name="kdf.hw" value="#(kdf.hw??)">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">单号</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.dh" name="kdf.dh" value="#(kdf.dh??)">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">快递公司</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.kdgs" name="kdf.kdgs" value="#(kdf.kdgs??)">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">重量</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.zl" name="kdf.zl" value="#(kdf.zl??)">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">价格</label>
                <div class="layui-input-inline">
                    <input type="text" data-number class="layui-input" id="kdf.jg" name="kdf.jg" value="#(kdf.jg??)">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">二级科目:</label>
                <div class="layui-input-inline">
                    <input type="text" name="kdf.ejkm" value='#(kdf.ejkm??)' placeholder="请输入二级科目" autocomplete="off" class="layui-input" list="ejkmList">
                    <datalist style="display: none" id="ejkmList">
                        #for(ejkm:ejkmList)
                        <option>#(ejkm??)</option>
                        #end
                    </datalist>
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">科目:</label>
                <div class="layui-input-inline">
                    <input type="text" name="kdf.km" value='#(kdf.km??)' placeholder="请输入科目" autocomplete="off" class="layui-input" list="kmList">
                    <datalist style="display: none" id="kmList">
                        #for(km:kmList)
                        <option>#(km??)</option>
                        #end
                    </datalist>
                </div>
            </div>
                    <datalist style="display: none" id="fsList">
                        #for(zffs:zffsList)
                        <option>#(zffs??)</option>
                        #end
                    </datalist>
        </div>

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">支付时间1:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zfsj1" id="zfsj1" value="#(kdf.zfsj1??)" placeholder="请输入支付时间1" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">支付金额1:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zfje1" value="#(kdf.zfje1??)" placeholder="请输入支付金额" data-number autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">支付方式1:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zffs1" value="#(kdf.zffs1??)" placeholder="请输入支付方式" autocomplete="off" class="layui-input" list="fsList">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">支付时间2:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zfsj2" id="zfsj2" value="#(kdf.zfsj2??)" placeholder="请输入支付时间2" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">支付金额2:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zfje2" value="#(kdf.zfje2??)" placeholder="请输入支付金额" data-number autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">支付方式2:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zffs2" value="#(kdf.zffs2??)" placeholder="请输入支付方式" autocomplete="off" class="layui-input" list="fsList">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">支付时间3:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zfsj3" id="zfsj3" value="#(kdf.zfsj3??)" placeholder="请输入支付时间3" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">支付金额3:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zfje3" value="#(kdf.zfje3??)" placeholder="请输入支付金额" data-number autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">支付方式3:</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="kdf.zffs3" value="#(kdf.zffs3??)" placeholder="请输入支付方式" autocomplete="off" class="layui-input" list="fsList">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">其他应付款:</label>
                <div class="layui-input-inline">
                    <input type="text" name="kdf.qtyfk" value="#(kdf.qtyfk??)" placeholder="请输入其他应付款金额" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">时效</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.sx" name="kdf.sx" value="#(kdf.sx??)">
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">填写人</label>
                <div class="layui-input-inline">
                    <select id="kdf.txr" name="kdf.txr"  lay-search="">
                        <option></option>
                        #for( account : accountList )
                        <option value="#(account.userName??)" #if(kdf.txr??==account.userName??) selected #end>#(account.userName??)</option>
                        #end
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">归属国家</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.gj" name="kdf.gj" value="#(kdf.gj??)" list="gbList">
                    <datalist id="gbList" style="display: none">
                        <option>美国</option>
                        <option>德国</option>
                        <option>澳大利亚</option>
                        <option>英国</option>
                        <option>法国</option>
                        <option>加拿大</option>
                        <option>新西兰</option>
                        <option>俄罗斯</option>
                        <option>荷兰</option>
                        <option>中国</option>
                    </datalist>
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">归属客户</label>
                <div class="layui-input-inline">
                    <select id="kdf.khid" name="kdf.khid"  lay-search="">
                    <option></option>
                    #for( kh : khList )
                    <option value="#(kh.id??)" #if(kdf.khid??==kh.id??) selected #end>#(kh.jc??)</option>
                    #end
                </select>
                </div>
            </div>
            <div class="form-inline">
                <label class="layui-form-label">归属订单</label>
                <div class="layui-input-inline">
                    <select id="kdf.ddbh" name="kdf.ddbh"  lay-search="">
                    <option></option>
                    #for( dd : ddList )
                    <option value="#(dd.ddbh??)" #if(kdf.ddbh??==dd.ddbh??) selected #end>#(dd.ddbh??)</option>
                    #end
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-inline w800">
                    <textarea name="kdf.bz" style="width: 100%; height: 80px;">#(kdf.bz??)</textarea>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="form-inline">
                <label class="layui-form-label">填写时间</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="kdf.txsj" name="kdf.txsj" value="#(kdf.txsj??)">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-inline margin-left-30">
                <input class="layui-btn layui-btn-sm" type="submit" value="提交"/>
            </div>
        </div>
    </form>
</div>
#end
#end

#define js()
<script type="text/javascript">
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        laydate.render({
            elem: document.getElementById('kdf.rq')
        });
        laydate.render({
            elem: document.getElementById('kdf.txsj')
        });

        laydate.render({
            elem: "#zfsj1"
        });
        laydate.render({
            elem: "#zfsj2"
        });
        laydate.render({
            elem: "#zfsj3"
        });
    });
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            ,beforeSerialize: function(){
                $("input[data-number]").each(function() {
                    var vaIn = $(this).val();
                    if (vaIn && !isIntNumber(vaIn) && !isFloatNumber(vaIn)) {
                        showFailMsg(vaIn + " 不能填非数字!");
                        return false;
                    }
                });
            }
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/kdf/edit?id="+ret.id);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });


</script>
#end
