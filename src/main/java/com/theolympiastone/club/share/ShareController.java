package com.theolympiastone.club.share;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Record;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Share;
import com.theolympiastone.club.common.pageview.PageViewInterceptor;
import com.theolympiastone.club.common.safe.RestTime;
import com.theolympiastone.club.my.favorite.FavoriteService;
import com.theolympiastone.club.my.like.LikeService;
import com.theolympiastone.club.my.share.MyShareService;
import com.theolympiastone.club.project.ProjectService;
import us.codecraft.webmagic.selector.Html;

import java.util.List;
import java.util.Map;

/**
 * 分享控制器
 */
@Before({FrontAuthInterceptor.class, ShareSeo.class})
public class ShareController extends BaseController {
    @Inject
    ShareService srv;
    @Inject
    ProjectService pSrv;

    /**
     * 首页
     */
    public void index() {
        keepPara();
        String queryShare = getPara("queryShare", "");
        String querystart = getPara("queryStart", "0000-00-00");
        String queryEnd = getPara("queryEnd", "9999-12-31");
        setAttr("queryShare", queryShare);
        setAttr("querystart", querystart);
        setAttr("queryEnd", queryEnd);
        setAttr("sharePage", srv.paginate(queryShare, querystart, queryEnd, getParaToInt("p", 1)));
        setAttr("hotShare", srv.getHotShare());
        render("index.html");
    }

    /**
     * 详情页
     */
    @Before(PageViewInterceptor.class)
    public void detail() {
        Integer id = getParaToInt();
        Share share = srv.findById(getLoginAccountId(), id);
        List<Record> viewList = srv.getViewList(id);
        if (share != null) {
            Html html = new Html(share.getContent());
            List<String> mp3List = html.xpath("//a[contains(@href,.mp3)]").all();
            List<Map<String, String>> mp3MapList = Lists.newArrayList();
            for (String mp3 : mp3List) {
                Html mp3Html = new Html(mp3);
                String title = mp3Html.xpath("//a/text()").toString();
                String url = mp3Html.xpath("//a/@href").toString();
                mp3MapList.add(ImmutableMap.of("title", title, "src", url));
            }
            String mp3Json = new Gson().toJson(mp3MapList);
            setAttr("share", share);
            setAttr("mp3Json", mp3Json.replace("\"", "'"));

            setAttr("viewList", viewList);
            setAttr("project", ProjectService.me.findById(share.getProjectId(), "id, name"));
            setAttr("replyPage", srv.getReplyPage(share.getId(), getParaToInt("p", 1)));
            setAttr("hotShare", srv.getHotShare());
            setAttr("accountList", pSrv.getAccountListJson());
            render("detail.html");

            setLikeAndFavoriteStatus(share);
        } else {
            renderError(404);
        }
    }

    /**
     * 如果用户已登录，则需要显示当前 article 是否已经被该用户点赞、收藏了
     */
    private void setLikeAndFavoriteStatus(Share share) {
        Ret ret = Ret.create();
        LikeService.me.setLikeStatus(getLoginAccount(), "share", share, ret);
        FavoriteService.me.setFavoriteStatus(getLoginAccount(), "share", share, ret);
        setAttr("ret", ret);
    }

    /**
     * 回复
     */
    public void saveReply() {
        if (notLogin()) {
            renderJson(Ret.fail("登录后才可以评论"));
            return;
        }
        String restTimeMsg = RestTime.checkRestTime(getLoginAccount());
        if (restTimeMsg != null) {
            renderJson(Ret.fail(restTimeMsg));
            return;
        }
        String replyContent = getPara("replyContent");
        if (StrKit.isBlank(replyContent)) {
            renderJson(Ret.fail("回复内容不能为空"));
            return;
        }

        Ret ret = srv.saveReply(getParaToInt("articleId"), getLoginAccountId(), replyContent);

        // 注入 nickName 与 avatar 便于 renderToString 生成 replyItem html 片段
        Account loginAccount = getLoginAccount();
        ret.set("loginAccount", loginAccount);
        // 用模板引擎生成 HTML 片段 replyItem
        String replyItem = renderToString("_reply_item.html", ret);

        ret.set("replyItem", replyItem);
        renderJson(ret);
    }

    /**
     * 删除回复
     */
    public void deleteReply() {
        if (isLogin()) {
            int accountId = getLoginAccountId();
            int replyId = getParaToInt("id");
            MyShareService.me.deleteShareReplyById(accountId, replyId);
            renderJson(Ret.ok());
        } else {
            renderJson(Ret.fail("未登录用户不会显示删除链接，请勿手工制造请求"));
        }
    }
}
