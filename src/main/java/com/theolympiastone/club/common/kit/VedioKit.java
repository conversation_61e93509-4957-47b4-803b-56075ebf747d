package com.theolympiastone.club.common.kit;

import com.jfinal.kit.StrKit;

public class VedioKit {
    private final static String[] imgExts = new String[]{".mp3", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"};

    public static String getExtName(String fileName) {
        return ImageKit.getExtName(fileName);
    }

    public static String getRealName(String fileName) {
        int index = fileName.lastIndexOf('.');
        if (index != -1 && (index + 1) < fileName.length()) {
            return fileName.substring(0, index - 1);
        } else {
            return null;
        }
    }

    public static boolean isVedioExtName(String fileName) {
        if (StrKit.isBlank(fileName)) {
            return false;
        }
        fileName = fileName.trim().toLowerCase();
        String ext = getExtName(fileName);
        if (ext != null) {
            for (String s : imgExts) {
                if (s.equals(ext)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean notVedioExtName(String fileName) {
        return !isVedioExtName(fileName);
    }
}
