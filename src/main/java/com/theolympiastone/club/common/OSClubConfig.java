package com.theolympiastone.club.common;

import com.alibaba.druid.filter.stat.StatFilter;
import com.alibaba.druid.wall.WallFilter;
import com.jfinal.config.*;
import com.jfinal.json.MixedJsonFactory;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Prop;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.cron4j.Cron4jPlugin;
import com.jfinal.plugin.druid.DruidPlugin;
import com.jfinal.plugin.ehcache.EhCachePlugin;
import com.jfinal.render.JsonRender;
import com.jfinal.server.undertow.UndertowServer;
import com.jfinal.template.Engine;
import com.jfinal.template.source.ClassPathSourceFactory;
import com.jfinal.upload.UploadConfig;
import com.theolympiastone.club._admin.auth.AdminAuthKit;
import com.theolympiastone.club._admin.common.Admin2Routes;
import com.theolympiastone.club._admin.common.AdminRoutes;
import com.theolympiastone.club._admin.permission.PermissionDirective;
import com.theolympiastone.club._admin.role.NoFactoryDirective;
import com.theolympiastone.club._admin.role.NoRoleDirective;
import com.theolympiastone.club._admin.role.RoleDirective;
import com.theolympiastone.club.common.handler.UrlSeoHandler;
import com.theolympiastone.club.common.interceptor.LoginSessionInterceptor;
import com.theolympiastone.club.common.kit.DruidKit;
import com.theolympiastone.club.common.kit.RandomUtil;
import com.theolympiastone.club.common.kit.StringKit;
import com.theolympiastone.club.common.kit.XKit;
import com.theolympiastone.club.common.model._MappingKit;
import com.theolympiastone.club.job.RedisCpYxkhSendEmailJob;
import com.theolympiastone.club.job.RedisSendEmailJob;
import com.theolympiastone.club.job.RedisYxkhSendEmailJob;
import com.theolympiastone.club.login.LoginService;
import com.theolympiastone.club.my.forum.ForumPermission;
import com.theolympiastone.club.my.friend.FriendInterceptor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * JFinalClubConfig
 */
public class OSClubConfig extends JFinalConfig {
    private static final Prop p = OSConstants.p;
    public static ThreadPoolExecutor threadPool = new ThreadPoolExecutor(2, 50, 300, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(50),
            r -> new Thread(r, "redis_cache_task_pool_" + r.hashCode()), new ThreadPoolExecutor.DiscardOldestPolicy());
    private WallFilter wallFilter;

    /**
     * 启动入口，运行此 main 方法可以启动项目，此main方法可以放置在任意的Class类定义中，不一定要放于此
     * <p>
     * 使用本方法启动过第一次以后，会在开发工具的 debug、run configuration 中自动生成
     * 一条启动配置项，可对该自动生成的配置再继续添加更多的配置项，例如 VM argument 可配置为:
     * -XX:PermSize=64M -XX:MaxPermSize=256M
     * 上述 VM 配置可以缓解热加载功能出现的异常
     */
    public static void main(String[] args) {
        System.out.println("=== 开始启动OSClub项目 ===");
        try {
            long startTime = System.currentTimeMillis();

            // 显示JVM信息
            Runtime runtime = Runtime.getRuntime();
            System.out.println("JVM信息: " + System.getProperty("java.version") +
                              ", 最大内存: " + (runtime.maxMemory() / 1024 / 1024) + "MB" +
                              ", 可用处理器: " + runtime.availableProcessors());

            System.out.println("正在创建UndertowServer...");
            long serverCreateTime = System.currentTimeMillis();
            UndertowServer server = UndertowServer.create(OSClubConfig.class);
            System.out.println("UndertowServer创建完成，耗时: " + (System.currentTimeMillis() - serverCreateTime) + "ms");

            System.out.println("开始配置服务器...");
            boolean devMode = p.getBoolean("devMode", true);
            server.setDevMode(devMode).setHost("0.0.0.0").setPort(3333);
            System.out.println("服务器配置完成，开发模式: " + devMode);

            System.out.println("开始启动服务器...");
            long serverStartTime = System.currentTimeMillis();
            server.start();
            System.out.println("服务器启动完成，耗时: " + (System.currentTimeMillis() - serverStartTime) + "ms");

            System.out.println("=== 项目启动完成，总耗时: " + (System.currentTimeMillis() - startTime) + "ms ===");
            System.out.println("访问地址: http://localhost:3333");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    public static DruidPlugin getDruidPlugin() {
        long startTime = System.currentTimeMillis();
        System.out.println("   开始创建数据库连接池...");

        // 创建Druid连接池
        DruidPlugin druidPlugin = new DruidPlugin(p.get("jdbcUrl"), p.get("user"), p.get("password").trim());

        // 应用Druid连接池优化配置 - 减小初始连接数以加快启动速度
        int initialSize = p.getInt("druid.initialSize", 1);
        int minIdle = p.getInt("druid.minIdle", 1);
        int maxActive = p.getInt("druid.maxActive", 20);

        System.out.println("   数据库连接池配置: initialSize=" + initialSize +
                          ", minIdle=" + minIdle +
                          ", maxActive=" + maxActive);

        druidPlugin.setInitialSize(initialSize);
        druidPlugin.setMinIdle(minIdle);
        druidPlugin.setMaxActive(maxActive);
        druidPlugin.setMaxWait(p.getInt("druid.maxWait", 5000));
        druidPlugin.setTimeBetweenEvictionRunsMillis(p.getLong("druid.timeBetweenEvictionRunsMillis", 60000L));
        druidPlugin.setMinEvictableIdleTimeMillis(p.getLong("druid.minEvictableIdleTimeMillis", 300000L));
        druidPlugin.setValidationQuery(p.get("druid.validationQuery", "SELECT 1"));
        druidPlugin.setTestWhileIdle(p.getBoolean("druid.testWhileIdle", true));
        druidPlugin.setTestOnBorrow(p.getBoolean("druid.testOnBorrow", false));
        druidPlugin.setTestOnReturn(p.getBoolean("druid.testOnReturn", false));
        druidPlugin.setMaxPoolPreparedStatementPerConnectionSize(
            p.getInt("druid.maxPoolPreparedStatementPerConnectionSize", 20)
        );

        System.out.println("   数据库连接池创建完成，耗时: " + (System.currentTimeMillis() - startTime) + "ms");
        return druidPlugin;
    }

    @Override
    public void configConstant(Constants me) {
        me.setDevMode(p.getBoolean("devMode", false));
        me.setMaxPostSize(1024 * 1024 * 50);
        me.setJsonFactory(MixedJsonFactory.me());
        me.setInjectDependency(true);
        me.setBaseDownloadPath("upload");
        me.setErrorView(400, "/_view/common/401.html");
        me.setErrorView(599, "/_view/common/599.html");
        me.setError401View("/_view/common/401.html");
        me.setError403View("/_view/common/403.html");
        me.setError404View("/_view/common/404.html");
        me.setError500View("/_view/common/500.html");
    }

    /**
     * 路由拆分到 FrontRutes 与 AdminRoutes 之中配置处:
     * 1:可分别配置不同的 baseViewPath 与 Interceptor
     * 2:避免多人协同开发时，频繁修改此文件带来的版本冲突
     * 3:避免本文件中内容过多，拆分后可读性增强
     * 4:便于分模块管理路由
     */
    @Override
    public void configRoute(Routes me) {
        me.add(new FrontRoutes());
        me.add(new PublicRoutes());
        me.add(new AdminRoutes());
        me.add(new Admin2Routes());
        me.add(new MobileRoutes());
    }

    /**
     * 配置模板引擎，通常情况只需配置共享的模板函数
     */
    @Override
    public void configEngine(Engine me) {
        me.setDevMode(p.getBoolean("engineDevMode", false));

        // 添加角色、权限指令
        me.addDirective("role", RoleDirective.class);
        me.addDirective("norole", NoRoleDirective.class);
        me.addDirective("nofactory", NoFactoryDirective.class);
        me.addDirective("permission", PermissionDirective.class);
        me.addDirective("perm", PermissionDirective.class);        // 配置一个别名指令

        // 添加角色、权限 shared method
        me.addSharedMethod(AdminAuthKit.class);
        me.addSharedMethod(StringKit.class);
        me.addSharedObject("ForumPermission", new ForumPermission());
        me.addSharedObject("xKit", new XKit());
        me.addSharedObject("RandomUtil", new RandomUtil());

        me.addSharedFunction("/_view/common/__layout.html");
        me.addSharedFunction("/_view/common/__layout_yxkh.html");
        me.addSharedFunction("/_view/mobile/__mLayout.html");
        me.addSharedFunction("/_view/common/__simple_layout.html");
        me.addSharedFunction("/_view/common/__new_simple_layout.html");
        me.addSharedFunction("/_view/common/__layui_layout.html");
        me.addSharedFunction("/_view/common/_paginate.html");
        me.addSharedFunction("/_view/common/kit.html");

        me.addSharedFunction("/_view/_admin/common/__admin_layout.html");
        me.addSharedFunction("/_view/_admin/common/_admin_paginate.html");
    }

    @Override
    public void configPlugin(Plugins me) {
        long startTime = System.currentTimeMillis();
        System.out.println("=== 开始配置插件 ===");

        // 配置数据库连接池
        long dbStartTime = System.currentTimeMillis();
        System.out.println("1. 开始创建DruidPlugin...");
        DruidPlugin druidPlugin = getDruidPlugin();
        System.out.println("   DruidPlugin创建完成，耗时: " + (System.currentTimeMillis() - dbStartTime) + "ms");

        wallFilter = new WallFilter();              // 加强数据库安全
        wallFilter.setDbType("mysql");
        druidPlugin.addFilter(wallFilter);
        druidPlugin.addFilter(new StatFilter());    // 添加 StatFilter 才会有统计数据
        System.out.println("2. 开始添加DruidPlugin到插件列表...");
        me.add(druidPlugin);
        System.out.println("   DruidPlugin添加完成，总耗时: " + (System.currentTimeMillis() - dbStartTime) + "ms");

        // 配置ActiveRecord
        long arpStartTime = System.currentTimeMillis();
        System.out.println("3. 开始配置ActiveRecord插件...");
        ActiveRecordPlugin arp = new ActiveRecordPlugin(druidPlugin);
        arp.setTransactionLevel(Connection.TRANSACTION_READ_COMMITTED);

        System.out.println("4. 开始映射数据库表...");
        long mappingStartTime = System.currentTimeMillis();
        _MappingKit.mapping(arp);
        System.out.println("   数据库表映射完成，耗时: " + (System.currentTimeMillis() - mappingStartTime) + "ms");

        // 强制指定复合主键的次序，避免不同的开发环境生成在 _MappingKit 中的复合主键次序不相同
        arp.setPrimaryKey("document", "mainMenu,subMenu");
        me.add(arp);
        arp.setShowSql(p.getBoolean("devMode", true));
        arp.getEngine().setSourceFactory(new ClassPathSourceFactory());

        System.out.println("5. 开始加载SQL模板...");
        long sqlStartTime = System.currentTimeMillis();
        arp.addSqlTemplate("/sql/all_sqls.sql");
        System.out.println("   SQL模板加载完成，耗时: " + (System.currentTimeMillis() - sqlStartTime) + "ms");
        System.out.println("   ActiveRecord配置完成，总耗时: " + (System.currentTimeMillis() - arpStartTime) + "ms");

        // 配置缓存
        long cacheStartTime = System.currentTimeMillis();
        System.out.println("6. 开始配置EhCache...");
        me.add(new EhCachePlugin());
        System.out.println("   EhCache配置完成，耗时: " + (System.currentTimeMillis() - cacheStartTime) + "ms");

        // 配置定时任务
        String configNameValue = p.get(Cron4jPlugin.defaultConfigName);
        if (!StringUtils.isEmpty(configNameValue)) {
            long cronStartTime = System.currentTimeMillis();
            System.out.println("7. 开始配置Cron4j定时任务...");
            me.add(new Cron4jPlugin(p));
            System.out.println("   Cron4j配置完成，耗时: " + (System.currentTimeMillis() - cronStartTime) + "ms");
        } else {
            System.out.println("7. 跳过Cron4j配置（配置为空）");
        }

        System.out.println("=== 所有插件配置完成，总耗时: " + (System.currentTimeMillis() - startTime) + "ms ===");
    }

    @Override
    public void configInterceptor(Interceptors me) {
        me.add(new LoginSessionInterceptor());
    }

    @Override
    public void configHandler(Handlers me) {
        me.add(DruidKit.getDruidStatViewHandler()); // druid 统计页面功能
        me.add(new UrlSeoHandler());                // index、detail 两类 action 的 url seo
    }

    /**
     * 本方法会在 jfinal 启动过程完成之后被回调，详见 jfinal 手册
     */
    @Override
    public void onStart() {
        long startTime = System.currentTimeMillis();
        System.out.println("=== 开始执行启动后回调 ===");

        UploadConfig.addWhitelist("m4a");
        // 配置JSON渲染排除属性
        System.out.println("1. 配置JSON渲染排除属性...");
        JsonRender.addExcludedAttrs(
                LoginService.loginAccountCacheName,
                LoginSessionInterceptor.remindKey,
                FriendInterceptor.followNum, FriendInterceptor.fansNum, FriendInterceptor.friendRelation
        );

        // 检查调试模式
        Boolean debug = p.getBoolean("debug", false);
        System.out.println("2. 调试模式: " + debug);

        // 延迟启动后台任务，避免阻塞主启动流程
        System.out.println("3. 延迟启动后台任务线程池...");
        long taskStartTime = System.currentTimeMillis();

        // 使用定时器延迟5秒启动后台任务，避免影响启动速度
        Timer delayedTaskTimer = new Timer("DelayedTaskStarter", true);
        delayedTaskTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                System.out.println("   开始启动延迟后台任务...");
                if (!debug) {
                    System.out.println("   启动邮件发送任务...");
                    threadPool.submit(new RedisSendEmailJob());
                    threadPool.submit(new RedisYxkhSendEmailJob(10));
                    threadPool.submit(new RedisCpYxkhSendEmailJob());
                } else {
                    System.out.println("   调试模式，跳过邮件发送任务");
                }

                System.out.println("   启动缓存任务...");
                threadPool.submit(new CacheKit());
                System.out.println("   延迟后台任务启动完成");
            }
        }, 5000); // 延迟5秒启动

        System.out.println("   后台任务已安排延迟启动，耗时: " + (System.currentTimeMillis() - taskStartTime) + "ms");

        // 输出路径信息
        System.out.println("4. 系统路径信息:");
        System.out.println("   WebRoot路径: " + PathKit.getWebRootPath());
        System.out.println("   工作目录: " + System.getProperty("user.dir"));

        // 配置Druid
        System.out.println("5. 配置Druid SQL过滤器...");
        wallFilter.getConfig().setSelectUnionCheck(false);

        System.out.println("=== 启动后回调完成，总耗时: " + (System.currentTimeMillis() - startTime) + "ms ===");
        System.out.println("=== JFinal应用启动完成！===");
    }
}






