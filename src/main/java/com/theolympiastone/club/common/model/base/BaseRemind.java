package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseRemind<M extends BaseRemind<M>> extends Model<M> implements IBean {

	public void setAccountId(java.lang.Integer accountId) {
		set("accountId", accountId);
	}
	
	public java.lang.Integer getAccountId() {
		return getInt("accountId");
	}
	
	public void setReferMe(java.lang.Integer referMe) {
		set("referMe", referMe);
	}
	
	public java.lang.Integer getReferMe() {
		return getInt("referMe");
	}
	
	public void setMessage(java.lang.Integer message) {
		set("message", message);
	}
	
	public java.lang.Integer getMessage() {
		return getInt("message");
	}
	
	public void setFans(java.lang.Integer fans) {
		set("fans", fans);
	}
	
	public java.lang.Integer getFans() {
		return getInt("fans");
	}
	
}

