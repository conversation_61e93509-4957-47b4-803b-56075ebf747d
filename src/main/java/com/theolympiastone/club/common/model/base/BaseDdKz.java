package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseDdKz<M extends BaseDdKz<M>> extends Model<M> implements IBean {

	public void setDdbh(java.lang.String ddbh) {
		set("ddbh", ddbh);
	}
	
	public java.lang.String getDdbh() {
		return getStr("ddbh");
	}
	
	public void setJkid(java.lang.String jkid) {
		set("jkid", jkid);
	}
	
	public java.lang.String getJkid() {
		return getStr("jkid");
	}
	
	public void setKhid(java.lang.Integer khid) {
		set("khid", khid);
	}
	
	public java.lang.Integer getKhid() {
		return getInt("khid");
	}
	
	public void setCdrq(java.lang.String cdrq) {
		set("cdrq", cdrq);
	}
	
	public java.lang.String getCdrq() {
		return getStr("cdrq");
	}
	
	public void setZgfs(java.lang.String zgfs) {
		set("zgfs", zgfs);
	}
	
	public java.lang.String getZgfs() {
		return getStr("zgfs");
	}
	
	public void setFkfs(java.lang.String fkfs) {
		set("fkfs", fkfs);
	}
	
	public java.lang.String getFkfs() {
		return getStr("fkfs");
	}
	
	public void setTzqr(java.lang.String tzqr) {
		set("tzqr", tzqr);
	}
	
	public java.lang.String getTzqr() {
		return getStr("tzqr");
	}
	
	public void setJdtx(java.lang.String jdtx) {
		set("jdtx", jdtx);
	}
	
	public java.lang.String getJdtx() {
		return getStr("jdtx");
	}
	
	public void setXsfp(java.lang.String xsfp) {
		set("xsfp", xsfp);
	}
	
	public java.lang.String getXsfp() {
		return getStr("xsfp");
	}
	
	public void setXdgc(java.lang.String xdgc) {
		set("xdgc", xdgc);
	}
	
	public java.lang.String getXdgc() {
		return getStr("xdgc");
	}
	
	public void setXdrq(java.lang.String xdrq) {
		set("xdrq", xdrq);
	}
	
	public java.lang.String getXdrq() {
		return getStr("xdrq");
	}
	
	public void setClqr(java.lang.String clqr) {
		set("clqr", clqr);
	}
	
	public java.lang.String getClqr() {
		return getStr("clqr");
	}
	
	public void setYgchrq(java.lang.String ygchrq) {
		set("ygchrq", ygchrq);
	}
	
	public java.lang.String getYgchrq() {
		return getStr("ygchrq");
	}
	
	public void setNsap(java.lang.String nsap) {
		set("nsap", nsap);
	}
	
	public java.lang.String getNsap() {
		return getStr("nsap");
	}
	
	public void setKzpb(java.lang.String kzpb) {
		set("kzpb", kzpb);
	}
	
	public java.lang.String getKzpb() {
		return getStr("kzpb");
	}
	
	public void setDqrq(java.lang.String dqrq) {
		set("dqrq", dqrq);
	}
	
	public java.lang.String getDqrq() {
		return getStr("dqrq");
	}
	
	public void setDkxz(java.lang.String dkxz) {
		set("dkxz", dkxz);
	}
	
	public java.lang.String getDkxz() {
		return getStr("dkxz");
	}
	
	public void setDmrq(java.lang.String dmrq) {
		set("dmrq", dmrq);
	}
	
	public java.lang.String getDmrq() {
		return getStr("dmrq");
	}
	
	public void setZxrq(java.lang.String zxrq) {
		set("zxrq", zxrq);
	}
	
	public java.lang.String getZxrq() {
		return getStr("zxrq");
	}
	
	public void setDctx(java.lang.String dctx) {
		set("dctx", dctx);
	}
	
	public java.lang.String getDctx() {
		return getStr("dctx");
	}
	
	public void setXztx(java.lang.String xztx) {
		set("xztx", xztx);
	}
	
	public java.lang.String getXztx() {
		return getStr("xztx");
	}
	
	public void setRmrq(java.lang.String rmrq) {
		set("rmrq", rmrq);
	}
	
	public java.lang.String getRmrq() {
		return getStr("rmrq");
	}
	
	public void setPskz(java.lang.String pskz) {
		set("pskz", pskz);
	}
	
	public java.lang.String getPskz() {
		return getStr("pskz");
	}
	
	public void setYhaprq(java.lang.String yhaprq) {
		set("yhaprq", yhaprq);
	}
	
	public java.lang.String getYhaprq() {
		return getStr("yhaprq");
	}
	
	public void setCptqr(java.lang.String cptqr) {
		set("cptqr", cptqr);
	}
	
	public java.lang.String getCptqr() {
		return getStr("cptqr");
	}
	
	public void setFhrq(java.lang.String fhrq) {
		set("fhrq", fhrq);
	}
	
	public java.lang.String getFhrq() {
		return getStr("fhrq");
	}
	
	public void setCq(java.lang.String cq) {
		set("cq", cq);
	}
	
	public java.lang.String getCq() {
		return getStr("cq");
	}
	
	public void setXzjs(java.lang.String xzjs) {
		set("xzjs", xzjs);
	}
	
	public java.lang.String getXzjs() {
		return getStr("xzjs");
	}
	
	public void setXzbz(java.lang.String xzbz) {
		set("xzbz", xzbz);
	}
	
	public java.lang.String getXzbz() {
		return getStr("xzbz");
	}
	
	public void setHgh(java.lang.String hgh) {
		set("hgh", hgh);
	}
	
	public java.lang.String getHgh() {
		return getStr("hgh");
	}
	
	public void setHch(java.lang.String hch) {
		set("hch", hch);
	}
	
	public java.lang.String getHch() {
		return getStr("hch");
	}
	
	public void setMdg(java.lang.String mdg) {
		set("mdg", mdg);
	}
	
	public java.lang.String getMdg() {
		return getStr("mdg");
	}
	
	public void setYjdgsj(java.lang.String yjdgsj) {
		set("yjdgsj", yjdgsj);
	}
	
	public java.lang.String getYjdgsj() {
		return getStr("yjdgsj");
	}
	
	public void setZdzz(java.lang.String zdzz) {
		set("zdzz", zdzz);
	}
	
	public java.lang.String getZdzz() {
		return getStr("zdzz");
	}
	
	public void setYfzd(java.lang.String yfzd) {
		set("yfzd", yfzd);
	}
	
	public java.lang.String getYfzd() {
		return getStr("yfzd");
	}
	
	public void setSftd(java.lang.String sftd) {
		set("sftd", sftd);
	}
	
	public java.lang.String getSftd() {
		return getStr("sftd");
	}
	
	public void setDgqyztx(java.lang.String dgqyztx) {
		set("dgqyztx", dgqyztx);
	}
	
	public java.lang.String getDgqyztx() {
		return getStr("dgqyztx");
	}
	
	public void setMdgqg(java.lang.String mdgqg) {
		set("mdgqg", mdgqg);
	}
	
	public java.lang.String getMdgqg() {
		return getStr("mdgqg");
	}
	
	public void setKcsh(java.lang.String kcsh) {
		set("kcsh", kcsh);
	}
	
	public java.lang.String getKcsh() {
		return getStr("kcsh");
	}
	
	public void setKhfk(java.lang.String khfk) {
		set("khfk", khfk);
	}
	
	public java.lang.String getKhfk() {
		return getStr("khfk");
	}
	
	public void setHkqk(java.lang.String hkqk) {
		set("hkqk", hkqk);
	}
	
	public java.lang.String getHkqk() {
		return getStr("hkqk");
	}
	
	public void setHkje(java.lang.String hkje) {
		set("hkje", hkje);
	}
	
	public java.lang.String getHkje() {
		return getStr("hkje");
	}
	
	public void setHksj(java.lang.String hksj) {
		set("hksj", hksj);
	}
	
	public java.lang.String getHksj() {
		return getStr("hksj");
	}
	
	public void setHkjedw(java.lang.String hkjedw) {
		set("hkjedw", hkjedw);
	}
	
	public java.lang.String getHkjedw() {
		return getStr("hkjedw");
	}
	
	public void setHkje1(java.lang.String hkje1) {
		set("hkje1", hkje1);
	}
	
	public java.lang.String getHkje1() {
		return getStr("hkje1");
	}
	
	public void setHksj1(java.lang.String hksj1) {
		set("hksj1", hksj1);
	}
	
	public java.lang.String getHksj1() {
		return getStr("hksj1");
	}
	
	public void setHkjedw1(java.lang.String hkjedw1) {
		set("hkjedw1", hkjedw1);
	}
	
	public java.lang.String getHkjedw1() {
		return getStr("hkjedw1");
	}
	
	public void setHkje2(java.lang.String hkje2) {
		set("hkje2", hkje2);
	}
	
	public java.lang.String getHkje2() {
		return getStr("hkje2");
	}
	
	public void setHksj2(java.lang.String hksj2) {
		set("hksj2", hksj2);
	}
	
	public java.lang.String getHksj2() {
		return getStr("hksj2");
	}
	
	public void setHkjedw2(java.lang.String hkjedw2) {
		set("hkjedw2", hkjedw2);
	}
	
	public java.lang.String getHkjedw2() {
		return getStr("hkjedw2");
	}
	
	public void setHkje3(java.lang.String hkje3) {
		set("hkje3", hkje3);
	}
	
	public java.lang.String getHkje3() {
		return getStr("hkje3");
	}
	
	public void setHksj3(java.lang.String hksj3) {
		set("hksj3", hksj3);
	}
	
	public java.lang.String getHksj3() {
		return getStr("hksj3");
	}
	
	public void setHkjedw3(java.lang.String hkjedw3) {
		set("hkjedw3", hkjedw3);
	}
	
	public java.lang.String getHkjedw3() {
		return getStr("hkjedw3");
	}
	
	public void setHkje4(java.lang.String hkje4) {
		set("hkje4", hkje4);
	}
	
	public java.lang.String getHkje4() {
		return getStr("hkje4");
	}
	
	public void setHksj4(java.lang.String hksj4) {
		set("hksj4", hksj4);
	}
	
	public java.lang.String getHksj4() {
		return getStr("hksj4");
	}
	
	public void setHkjedw4(java.lang.String hkjedw4) {
		set("hkjedw4", hkjedw4);
	}
	
	public java.lang.String getHkjedw4() {
		return getStr("hkjedw4");
	}
	
	public void setHkje5(java.lang.String hkje5) {
		set("hkje5", hkje5);
	}
	
	public java.lang.String getHkje5() {
		return getStr("hkje5");
	}
	
	public void setHkje6(java.lang.String hkje6) {
		set("hkje6", hkje6);
	}
	
	public java.lang.String getHkje6() {
		return getStr("hkje6");
	}
	
	public void setHkje7(java.lang.String hkje7) {
		set("hkje7", hkje7);
	}
	
	public java.lang.String getHkje7() {
		return getStr("hkje7");
	}
	
	public void setHkje8(java.lang.String hkje8) {
		set("hkje8", hkje8);
	}
	
	public java.lang.String getHkje8() {
		return getStr("hkje8");
	}
	
	public void setHkje9(java.lang.String hkje9) {
		set("hkje9", hkje9);
	}
	
	public java.lang.String getHkje9() {
		return getStr("hkje9");
	}
	
	public void setHkje10(java.lang.String hkje10) {
		set("hkje10", hkje10);
	}
	
	public java.lang.String getHkje10() {
		return getStr("hkje10");
	}
	
	public void setHkje11(java.lang.String hkje11) {
		set("hkje11", hkje11);
	}
	
	public java.lang.String getHkje11() {
		return getStr("hkje11");
	}
	
	public void setHkje12(java.lang.String hkje12) {
		set("hkje12", hkje12);
	}
	
	public java.lang.String getHkje12() {
		return getStr("hkje12");
	}
	
	public void setHksj5(java.lang.String hksj5) {
		set("hksj5", hksj5);
	}
	
	public java.lang.String getHksj5() {
		return getStr("hksj5");
	}
	
	public void setHksj6(java.lang.String hksj6) {
		set("hksj6", hksj6);
	}
	
	public java.lang.String getHksj6() {
		return getStr("hksj6");
	}
	
	public void setHksj7(java.lang.String hksj7) {
		set("hksj7", hksj7);
	}
	
	public java.lang.String getHksj7() {
		return getStr("hksj7");
	}
	
	public void setHksj8(java.lang.String hksj8) {
		set("hksj8", hksj8);
	}
	
	public java.lang.String getHksj8() {
		return getStr("hksj8");
	}
	
	public void setHksj9(java.lang.String hksj9) {
		set("hksj9", hksj9);
	}
	
	public java.lang.String getHksj9() {
		return getStr("hksj9");
	}
	
	public void setHksj10(java.lang.String hksj10) {
		set("hksj10", hksj10);
	}
	
	public java.lang.String getHksj10() {
		return getStr("hksj10");
	}
	
	public void setHksj11(java.lang.String hksj11) {
		set("hksj11", hksj11);
	}
	
	public java.lang.String getHksj11() {
		return getStr("hksj11");
	}
	
	public void setHksj12(java.lang.String hksj12) {
		set("hksj12", hksj12);
	}
	
	public java.lang.String getHksj12() {
		return getStr("hksj12");
	}
	
	public void setHkjedw5(java.lang.String hkjedw5) {
		set("hkjedw5", hkjedw5);
	}
	
	public java.lang.String getHkjedw5() {
		return getStr("hkjedw5");
	}
	
	public void setHkjedw6(java.lang.String hkjedw6) {
		set("hkjedw6", hkjedw6);
	}
	
	public java.lang.String getHkjedw6() {
		return getStr("hkjedw6");
	}
	
	public void setHkjedw7(java.lang.String hkjedw7) {
		set("hkjedw7", hkjedw7);
	}
	
	public java.lang.String getHkjedw7() {
		return getStr("hkjedw7");
	}
	
	public void setHkjedw8(java.lang.String hkjedw8) {
		set("hkjedw8", hkjedw8);
	}
	
	public java.lang.String getHkjedw8() {
		return getStr("hkjedw8");
	}
	
	public void setHkjedw9(java.lang.String hkjedw9) {
		set("hkjedw9", hkjedw9);
	}
	
	public java.lang.String getHkjedw9() {
		return getStr("hkjedw9");
	}
	
	public void setHkjedw10(java.lang.String hkjedw10) {
		set("hkjedw10", hkjedw10);
	}
	
	public java.lang.String getHkjedw10() {
		return getStr("hkjedw10");
	}
	
	public void setHkjedw11(java.lang.String hkjedw11) {
		set("hkjedw11", hkjedw11);
	}
	
	public java.lang.String getHkjedw11() {
		return getStr("hkjedw11");
	}
	
	public void setHkjedw12(java.lang.String hkjedw12) {
		set("hkjedw12", hkjedw12);
	}
	
	public java.lang.String getHkjedw12() {
		return getStr("hkjedw12");
	}
	
	public void setHkfs(java.lang.String hkfs) {
		set("hkfs", hkfs);
	}
	
	public java.lang.String getHkfs() {
		return getStr("hkfs");
	}
	
	public void setHkfs1(java.lang.String hkfs1) {
		set("hkfs1", hkfs1);
	}
	
	public java.lang.String getHkfs1() {
		return getStr("hkfs1");
	}
	
	public void setHkfs2(java.lang.String hkfs2) {
		set("hkfs2", hkfs2);
	}
	
	public java.lang.String getHkfs2() {
		return getStr("hkfs2");
	}
	
	public void setHkfs3(java.lang.String hkfs3) {
		set("hkfs3", hkfs3);
	}
	
	public java.lang.String getHkfs3() {
		return getStr("hkfs3");
	}
	
	public void setHkfs4(java.lang.String hkfs4) {
		set("hkfs4", hkfs4);
	}
	
	public java.lang.String getHkfs4() {
		return getStr("hkfs4");
	}
	
	public void setHkfs5(java.lang.String hkfs5) {
		set("hkfs5", hkfs5);
	}
	
	public java.lang.String getHkfs5() {
		return getStr("hkfs5");
	}
	
	public void setHkfs6(java.lang.String hkfs6) {
		set("hkfs6", hkfs6);
	}
	
	public java.lang.String getHkfs6() {
		return getStr("hkfs6");
	}
	
	public void setHkfs7(java.lang.String hkfs7) {
		set("hkfs7", hkfs7);
	}
	
	public java.lang.String getHkfs7() {
		return getStr("hkfs7");
	}
	
	public void setHkfs8(java.lang.String hkfs8) {
		set("hkfs8", hkfs8);
	}
	
	public java.lang.String getHkfs8() {
		return getStr("hkfs8");
	}
	
	public void setHkfs9(java.lang.String hkfs9) {
		set("hkfs9", hkfs9);
	}
	
	public java.lang.String getHkfs9() {
		return getStr("hkfs9");
	}
	
	public void setHkfs10(java.lang.String hkfs10) {
		set("hkfs10", hkfs10);
	}
	
	public java.lang.String getHkfs10() {
		return getStr("hkfs10");
	}
	
	public void setHkfs11(java.lang.String hkfs11) {
		set("hkfs11", hkfs11);
	}
	
	public java.lang.String getHkfs11() {
		return getStr("hkfs11");
	}
	
	public void setHkfs12(java.lang.String hkfs12) {
		set("hkfs12", hkfs12);
	}
	
	public java.lang.String getHkfs12() {
		return getStr("hkfs12");
	}
	
	public void setSjjkhl(java.lang.String sjjkhl) {
		set("sjjkhl", sjjkhl);
	}
	
	public java.lang.String getSjjkhl() {
		return getStr("sjjkhl");
	}
	
	public void setSjjkhl1(java.lang.String sjjkhl1) {
		set("sjjkhl1", sjjkhl1);
	}
	
	public java.lang.String getSjjkhl1() {
		return getStr("sjjkhl1");
	}
	
	public void setSjjkhl2(java.lang.String sjjkhl2) {
		set("sjjkhl2", sjjkhl2);
	}
	
	public java.lang.String getSjjkhl2() {
		return getStr("sjjkhl2");
	}
	
	public void setSjjkhl3(java.lang.String sjjkhl3) {
		set("sjjkhl3", sjjkhl3);
	}
	
	public java.lang.String getSjjkhl3() {
		return getStr("sjjkhl3");
	}
	
	public void setSjjkhl4(java.lang.String sjjkhl4) {
		set("sjjkhl4", sjjkhl4);
	}
	
	public java.lang.String getSjjkhl4() {
		return getStr("sjjkhl4");
	}
	
	public void setSjjkhl5(java.lang.String sjjkhl5) {
		set("sjjkhl5", sjjkhl5);
	}
	
	public java.lang.String getSjjkhl5() {
		return getStr("sjjkhl5");
	}
	
	public void setSjjkhl6(java.lang.String sjjkhl6) {
		set("sjjkhl6", sjjkhl6);
	}
	
	public java.lang.String getSjjkhl6() {
		return getStr("sjjkhl6");
	}
	
	public void setSjjkhl7(java.lang.String sjjkhl7) {
		set("sjjkhl7", sjjkhl7);
	}
	
	public java.lang.String getSjjkhl7() {
		return getStr("sjjkhl7");
	}
	
	public void setSjjkhl8(java.lang.String sjjkhl8) {
		set("sjjkhl8", sjjkhl8);
	}
	
	public java.lang.String getSjjkhl8() {
		return getStr("sjjkhl8");
	}
	
	public void setSjjkhl9(java.lang.String sjjkhl9) {
		set("sjjkhl9", sjjkhl9);
	}
	
	public java.lang.String getSjjkhl9() {
		return getStr("sjjkhl9");
	}
	
	public void setSjjkhl10(java.lang.String sjjkhl10) {
		set("sjjkhl10", sjjkhl10);
	}
	
	public java.lang.String getSjjkhl10() {
		return getStr("sjjkhl10");
	}
	
	public void setSjjkhl11(java.lang.String sjjkhl11) {
		set("sjjkhl11", sjjkhl11);
	}
	
	public java.lang.String getSjjkhl11() {
		return getStr("sjjkhl11");
	}
	
	public void setSjjkhl12(java.lang.String sjjkhl12) {
		set("sjjkhl12", sjjkhl12);
	}
	
	public java.lang.String getSjjkhl12() {
		return getStr("sjjkhl12");
	}
	
	public void setWhkhl(java.lang.String whkhl) {
		set("whkhl", whkhl);
	}
	
	public java.lang.String getWhkhl() {
		return getStr("whkhl");
	}
	
	public void setDduyf(java.lang.String dduyf) {
		set("dduyf", dduyf);
	}
	
	public java.lang.String getDduyf() {
		return getStr("dduyf");
	}
	
	public void setZje(java.lang.String zje) {
		set("zje", zje);
	}
	
	public java.lang.String getZje() {
		return getStr("zje");
	}
	
	public void setZjedw(java.lang.String zjedw) {
		set("zjedw", zjedw);
	}
	
	public java.lang.String getZjedw() {
		return getStr("zjedw");
	}
	
	public void setSpje(java.lang.String spje) {
		set("spje", spje);
	}
	
	public java.lang.String getSpje() {
		return getStr("spje");
	}
	
	public void setSpjedw(java.lang.String spjedw) {
		set("spjedw", spjedw);
	}
	
	public java.lang.String getSpjedw() {
		return getStr("spjedw");
	}
	
	public void setQtce(java.lang.String qtce) {
		set("qtce", qtce);
	}
	
	public java.lang.String getQtce() {
		return getStr("qtce");
	}
	
	public void setCs(java.lang.String cs) {
		set("cs", cs);
	}
	
	public java.lang.String getCs() {
		return getStr("cs");
	}
	
	public void setDdzt(java.lang.String ddzt) {
		set("ddzt", ddzt);
	}
	
	public java.lang.String getDdzt() {
		return getStr("ddzt");
	}
	
	public void setSjdj(java.lang.String sjdj) {
		set("sjdj", sjdj);
	}
	
	public java.lang.String getSjdj() {
		return getStr("sjdj");
	}
	
	public void setYf(java.lang.String yf) {
		set("yf", yf);
	}
	
	public java.lang.String getYf() {
		return getStr("yf");
	}
	
	public void setGz(java.lang.String gz) {
		set("gz", gz);
	}
	
	public java.lang.String getGz() {
		return getStr("gz");
	}
	
	public void setQg(java.lang.String qg) {
		set("qg", qg);
	}
	
	public java.lang.String getQg() {
		return getStr("qg");
	}
	
	public void setSh(java.lang.String sh) {
		set("sh", sh);
	}
	
	public java.lang.String getSh() {
		return getStr("sh");
	}
	
	public void setSj(java.lang.String sj) {
		set("sj", sj);
	}
	
	public java.lang.String getSj() {
		return getStr("sj");
	}
	
	public void setYfbz(java.lang.String yfbz) {
		set("yfbz", yfbz);
	}
	
	public java.lang.String getYfbz() {
		return getStr("yfbz");
	}
	
	public void setGzbz(java.lang.String gzbz) {
		set("gzbz", gzbz);
	}
	
	public java.lang.String getGzbz() {
		return getStr("gzbz");
	}
	
	public void setQgbz(java.lang.String qgbz) {
		set("qgbz", qgbz);
	}
	
	public java.lang.String getQgbz() {
		return getStr("qgbz");
	}
	
	public void setShbz(java.lang.String shbz) {
		set("shbz", shbz);
	}
	
	public java.lang.String getShbz() {
		return getStr("shbz");
	}
	
	public void setSjbz(java.lang.String sjbz) {
		set("sjbz", sjbz);
	}
	
	public java.lang.String getSjbz() {
		return getStr("sjbz");
	}
	
	public void setYfhl(java.lang.String yfhl) {
		set("yfhl", yfhl);
	}
	
	public java.lang.String getYfhl() {
		return getStr("yfhl");
	}
	
	public void setGzhl(java.lang.String gzhl) {
		set("gzhl", gzhl);
	}
	
	public java.lang.String getGzhl() {
		return getStr("gzhl");
	}
	
	public void setQghl(java.lang.String qghl) {
		set("qghl", qghl);
	}
	
	public java.lang.String getQghl() {
		return getStr("qghl");
	}
	
	public void setShhl(java.lang.String shhl) {
		set("shhl", shhl);
	}
	
	public java.lang.String getShhl() {
		return getStr("shhl");
	}
	
	public void setSjhl(java.lang.String sjhl) {
		set("sjhl", sjhl);
	}
	
	public java.lang.String getSjhl() {
		return getStr("sjhl");
	}
	
	public void setHcb1(java.lang.String hcb1) {
		set("hcb1", hcb1);
	}
	
	public java.lang.String getHcb1() {
		return getStr("hcb1");
	}
	
	public void setHcb2(java.lang.String hcb2) {
		set("hcb2", hcb2);
	}
	
	public java.lang.String getHcb2() {
		return getStr("hcb2");
	}
	
	public void setHcb3(java.lang.String hcb3) {
		set("hcb3", hcb3);
	}
	
	public java.lang.String getHcb3() {
		return getStr("hcb3");
	}
	
	public void setHcb4(java.lang.String hcb4) {
		set("hcb4", hcb4);
	}
	
	public java.lang.String getHcb4() {
		return getStr("hcb4");
	}
	
	public void setTc1(java.lang.String tc1) {
		set("tc1", tc1);
	}
	
	public java.lang.String getTc1() {
		return getStr("tc1");
	}
	
	public void setTc2(java.lang.String tc2) {
		set("tc2", tc2);
	}
	
	public java.lang.String getTc2() {
		return getStr("tc2");
	}
	
	public void setSpgccdk(java.lang.String spgccdk) {
		set("spgccdk", spgccdk);
	}
	
	public java.lang.String getSpgccdk() {
		return getStr("spgccdk");
	}
	
	public void setSpywcdk(java.lang.String spywcdk) {
		set("spywcdk", spywcdk);
	}
	
	public java.lang.String getSpywcdk() {
		return getStr("spywcdk");
	}
	
	public void setSphdcdk(java.lang.String sphdcdk) {
		set("sphdcdk", sphdcdk);
	}
	
	public java.lang.String getSphdcdk() {
		return getStr("sphdcdk");
	}
	
	public void setSpqtcdk(java.lang.String spqtcdk) {
		set("spqtcdk", spqtcdk);
	}
	
	public java.lang.String getSpqtcdk() {
		return getStr("spqtcdk");
	}
	
	public void setHzyf(java.lang.String hzyf) {
		set("hzyf", hzyf);
	}
	
	public java.lang.String getHzyf() {
		return getStr("hzyf");
	}
	
	public void setTchyf(java.lang.String tchyf) {
		set("tchyf", tchyf);
	}
	
	public java.lang.String getTchyf() {
		return getStr("tchyf");
	}
	
	public void setTcsp(java.lang.String tcsp) {
		set("tcsp", tcsp);
	}
	
	public java.lang.String getTcsp() {
		return getStr("tcsp");
	}
	
	public void setHz(java.lang.String hz) {
		set("hz", hz);
	}
	
	public java.lang.String getHz() {
		return getStr("hz");
	}
	
	public void setLrsj(java.lang.String lrsj) {
		set("lrsj", lrsj);
	}
	
	public java.lang.String getLrsj() {
		return getStr("lrsj");
	}
	
	public void setHl(java.lang.String hl) {
		set("hl", hl);
	}
	
	public java.lang.String getHl() {
		return getStr("hl");
	}
	
	public void setGxsl(java.lang.String gxsl) {
		set("gxsl", gxsl);
	}
	
	public java.lang.String getGxsl() {
		return getStr("gxsl");
	}
	
	public void setGclx(java.lang.String gclx) {
		set("gclx", gclx);
	}
	
	public java.lang.String getGclx() {
		return getStr("gclx");
	}
	
	public void setLx(java.lang.String lx) {
		set("lx", lx);
	}
	
	public java.lang.String getLx() {
		return getStr("lx");
	}
	
	public void setEwsr(java.lang.String ewsr) {
		set("ewsr", ewsr);
	}
	
	public java.lang.String getEwsr() {
		return getStr("ewsr");
	}
	
	public void setEwfy(java.lang.String ewfy) {
		set("ewfy", ewfy);
	}
	
	public java.lang.String getEwfy() {
		return getStr("ewfy");
	}
	
	public void setClcb(java.lang.String clcb) {
		set("clcb", clcb);
	}
	
	public java.lang.String getClcb() {
		return getStr("clcb");
	}
	
	public void setCclcb(java.lang.String cclcb) {
		set("cclcb", cclcb);
	}
	
	public java.lang.String getCclcb() {
		return getStr("cclcb");
	}
	
	public void setDmZcs(java.lang.String dmZcs) {
		set("dm_zcs", dmZcs);
	}
	
	public java.lang.String getDmZcs() {
		return getStr("dm_zcs");
	}
	
	public void setRmZcs(java.lang.String rmZcs) {
		set("rm_zcs", rmZcs);
	}
	
	public java.lang.String getRmZcs() {
		return getStr("rm_zcs");
	}
	
	public void setZxZcs(java.lang.String zxZcs) {
		set("zx_zcs", zxZcs);
	}
	
	public java.lang.String getZxZcs() {
		return getStr("zx_zcs");
	}
	
	public void setClFj(java.lang.String clFj) {
		set("cl_fj", clFj);
	}
	
	public java.lang.String getClFj() {
		return getStr("cl_fj");
	}
	
	public void setAlje(java.lang.String alje) {
		set("alje", alje);
	}
	
	public java.lang.String getAlje() {
		return getStr("alje");
	}
	
	public void setDmZje(java.lang.String dmZje) {
		set("dm_zje", dmZje);
	}
	
	public java.lang.String getDmZje() {
		return getStr("dm_zje");
	}
	
	public void setRmZje(java.lang.String rmZje) {
		set("rm_zje", rmZje);
	}
	
	public java.lang.String getRmZje() {
		return getStr("rm_zje");
	}
	
	public void setZxZje(java.lang.String zxZje) {
		set("zx_zje", zxZje);
	}
	
	public java.lang.String getZxZje() {
		return getStr("zx_zje");
	}
	
	public void setDmCc(java.lang.String dmCc) {
		set("dm_cc", dmCc);
	}
	
	public java.lang.String getDmCc() {
		return getStr("dm_cc");
	}
	
	public void setRmCc(java.lang.String rmCc) {
		set("rm_cc", rmCc);
	}
	
	public java.lang.String getRmCc() {
		return getStr("rm_cc");
	}
	
	public void setZxCc(java.lang.String zxCc) {
		set("zx_cc", zxCc);
	}
	
	public java.lang.String getZxCc() {
		return getStr("zx_cc");
	}
	
	public void setDmFj(java.lang.String dmFj) {
		set("dm_fj", dmFj);
	}
	
	public java.lang.String getDmFj() {
		return getStr("dm_fj");
	}
	
	public void setRmFj(java.lang.String rmFj) {
		set("rm_fj", rmFj);
	}
	
	public java.lang.String getRmFj() {
		return getStr("rm_fj");
	}
	
	public void setZxFj(java.lang.String zxFj) {
		set("zx_fj", zxFj);
	}
	
	public java.lang.String getZxFj() {
		return getStr("zx_fj");
	}
	
	public void setHkbz(java.lang.String hkbz) {
		set("hkbz", hkbz);
	}
	
	public java.lang.String getHkbz() {
		return getStr("hkbz");
	}
	
	public void setHkbz1(java.lang.String hkbz1) {
		set("hkbz1", hkbz1);
	}
	
	public java.lang.String getHkbz1() {
		return getStr("hkbz1");
	}
	
	public void setHkbz2(java.lang.String hkbz2) {
		set("hkbz2", hkbz2);
	}
	
	public java.lang.String getHkbz2() {
		return getStr("hkbz2");
	}
	
	public void setHkbz3(java.lang.String hkbz3) {
		set("hkbz3", hkbz3);
	}
	
	public java.lang.String getHkbz3() {
		return getStr("hkbz3");
	}
	
	public void setHkbz4(java.lang.String hkbz4) {
		set("hkbz4", hkbz4);
	}
	
	public java.lang.String getHkbz4() {
		return getStr("hkbz4");
	}
	
	public void setHkbz5(java.lang.String hkbz5) {
		set("hkbz5", hkbz5);
	}
	
	public java.lang.String getHkbz5() {
		return getStr("hkbz5");
	}
	
	public void setHkbz6(java.lang.String hkbz6) {
		set("hkbz6", hkbz6);
	}
	
	public java.lang.String getHkbz6() {
		return getStr("hkbz6");
	}
	
	public void setHkbz7(java.lang.String hkbz7) {
		set("hkbz7", hkbz7);
	}
	
	public java.lang.String getHkbz7() {
		return getStr("hkbz7");
	}
	
	public void setHkbz8(java.lang.String hkbz8) {
		set("hkbz8", hkbz8);
	}
	
	public java.lang.String getHkbz8() {
		return getStr("hkbz8");
	}
	
	public void setHkbz9(java.lang.String hkbz9) {
		set("hkbz9", hkbz9);
	}
	
	public java.lang.String getHkbz9() {
		return getStr("hkbz9");
	}
	
	public void setHkbz10(java.lang.String hkbz10) {
		set("hkbz10", hkbz10);
	}
	
	public java.lang.String getHkbz10() {
		return getStr("hkbz10");
	}
	
	public void setHkbz11(java.lang.String hkbz11) {
		set("hkbz11", hkbz11);
	}
	
	public java.lang.String getHkbz11() {
		return getStr("hkbz11");
	}
	
	public void setHkbz12(java.lang.String hkbz12) {
		set("hkbz12", hkbz12);
	}
	
	public java.lang.String getHkbz12() {
		return getStr("hkbz12");
	}
	
}

