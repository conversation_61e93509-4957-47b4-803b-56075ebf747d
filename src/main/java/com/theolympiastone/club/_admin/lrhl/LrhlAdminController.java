package com.theolympiastone.club._admin.lrhl;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.model.Lrhl;

public class LrhlAdminController extends BaseController {
    @Inject
    LrhlAdminService srv;

    public void index() {
        keepPara();
        Page<Lrhl> lrhlPage = srv.paginate(getParaToInt("p", 1));
        setAttr("page", lrhlPage);
        render("index.html");
    }

    public void add() {
        render("add_edit.html");
    }

    @Before(LrhlAdminValidator.class)
    public void save() {
        Lrhl lrhl = getBean(Lrhl.class, true);
        Ret ret = srv.save(lrhl);
        renderJson(ret.set("id", lrhl.getId()));
    }

    public void edit() {
        keepPara();
        Lrhl lrhl = srv.findById(getParaToInt("id"));
        setAttr("lrhl", lrhl);
        render("add_edit.html");
    }

    @Before(LrhlAdminValidator.class)
    public void update() {
        Lrhl lrhl = getBean(Lrhl.class, true);
        Ret ret = srv.update(lrhl);
        renderJson(ret.set("id", lrhl.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }
}
