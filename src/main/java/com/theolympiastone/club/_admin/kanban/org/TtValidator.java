package com.theolympiastone.club._admin.kanban.org;

import com.jfinal.core.Controller;
import com.jfinal.validate.Validator;

/**
 * Created by LiuYB
 * date: 2018/11/29
 * time: 3:09
 */
public class TtValidator extends Validator {

    @Override
    protected void validate(Controller c) {
        setShortCircuit(true);
        validateRequired("jgid", "msg", "机构不能为空");
        validateRequired("tdid", "msg", "团队不能为空");
        validateRequired("cyid", "msg", "成员不能为空");
    }

    @Override
    protected void handleError(Controller c) {
        c.setAttr("state", "fail");
        c.renderJson();
    }
}
