package com.theolympiastone.club._admin;

import org.apache.commons.io.FileUtils;

import java.io.File;

/**
 * Created by LiuYB
 * date: 2019/4/10
 * time: 14:09
 */
public class WdsBatchDelImg {
    public static void main(String[] args) {
        getFileList("D:\\phpnow\\htdocs\\images\\in-stock");
        getFileList("D:\\phpnow\\htdocs\\images\\order-monuments");
    }

    private static void getFileList(String strPath) {
        File dir = new File(strPath);
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }
        for (File file : files) {
            String fileName = file.getName();
            if (file.isDirectory()) {
                getFileList(file.getAbsolutePath());
            } else if (fileName.contains("_s.")) {
                String strFileName = file.getAbsolutePath();
                FileUtils.deleteQuietly(file);
                System.out.println("remove:" + strFileName);
            }
        }
    }
}
