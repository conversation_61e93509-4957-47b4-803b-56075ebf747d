package com.theolympiastone.club.my.rck;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.Base64Utils;
import com.theolympiastone.club.common.kit.EmailKit;
import com.theolympiastone.club.common.model.Rck;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.theolympiastone.club.common.kit.StringKit.*;
import static com.theolympiastone.club.common.kit.UrlKit.getFileName;

@Before({FrontAuthInterceptor.class})
public class MyRckController extends BaseController {
    @Inject
    MyRckService srv;

    public void index() {
        keepPara();
        String query = getPara("query", "");
        setAttr("query", query);
        Page<Rck> rckPage = srv.paginate(getParaToInt("p", 1), query);
        setAttr("page", rckPage);
        render("index.html");
    }

    public void xin() {
        String query = getPara("query", "");
        String queryKsrq = getPara("queryKsrq");
        String queryJsrq = getPara("queryJsrq");
        String queryRq = getPara("queryRq");
        if (StringUtils.isEmpty(queryRq)) {
            queryKsrq = StringUtils.isEmpty(queryKsrq) ? yyyy_MM_dd() : queryKsrq;
            queryJsrq = StringUtils.isEmpty(queryJsrq) ? yyyy_MM_dd() : queryJsrq;
        }
        String scrq = getPara("scrq", yyyy_MM_dd());
        setAttr("query", query);
        setAttr("queryKsrq", queryKsrq);
        setAttr("queryJsrq", queryJsrq);
        setAttr("queryRq", queryRq);
        setAttr("scrq", scrq);
        List<Record> rqList = Db.find("select rq, concat(rq, '(', count(*), ')') text from rck group by rq;");
        Page<Rck> rckPage = srv.paginate(getParaToInt("p", 1), query, queryKsrq, queryJsrq, queryRq);
        setAttr("page", rckPage);
        setAttr("rqList", rqList);
        render("xin.html");
    }

    public void tpscwc() throws UnsupportedEncodingException {
        String rq = get("rq", yyyy_MM_dd());
        List<Record> records = Db.find("select * from rck where rq=?", rq);

        List<String> fileList = Lists.newArrayList();
        for (Record record : records) {
            String id = record.getStr("id");
            String wj = record.getStr("wj");
            if (StringUtils.isEmpty(wj)) {
                continue;
            }
            String[] wjSplit = wj.split(";");
            String filePath = PathKit.getWebRootPath() + "/upload/rck/" + id + "/" + wjSplit[0];
            fileList.add(filePath);
        }
        Collections.sort(fileList);

        EmailKit.sendEmail(Lists.newArrayList("<EMAIL>", "<EMAIL>"), rq + " 人才库简历上传完成, ", "<a href='http://360.theolympiastone.com/my/rck/xin?queryKsrq=" + rq + "&queryJsrq=" + rq + "' target='_blank'>快速跳转到简历库页面</a>", "", fileList);
        renderJson(Ret.ok("通知成功!"));
    }

    public void xgt() {
        String path = getPara("path", "");
        String fileName = getFileName(path);
        setAttr("fileName", fileName);
        setAttr("path", path);
        render("xgt.html");
    }

    public void add() {
        setAttr("isAdd", true);
        render("add_edit.html");
    }

    @Before(MyRckValidator.class)
    public void save() {
        List<UploadFile> files = getFiles();
        Rck rck = getBean(Rck.class);
        rck.setRq(yyyy_MM_dd());
        Ret ret = srv.save(rck);
        if (files != null) {
            String uploadPath = PathKit.getWebRootPath() + "/upload/rck/" + rck.getId();
            try {
                FileUtils.forceMkdir(new File(uploadPath));
            } catch (IOException e) {
                e.printStackTrace();
            }
            StringBuilder wj = new StringBuilder();
            for (UploadFile file : files) {
                try {
                    String fileName = file.getFileName();
                    File destFile = new File(uploadPath + "/" + fileName);
                    FileUtils.deleteQuietly(destFile);
                    FileUtils.moveFile(file.getFile(), destFile);
                    wj.append(fileName).append(";");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            rck.setWj(wj.toString());
        }
        srv.update(rck);
        if (!StringUtils.isEmpty(rck.getPs())) {
            email(rck);
        }
        renderJson(ret.set("id", rck.getId()));
    }

    private static void email(Rck rck) {
        if (!StringUtils.isEmpty(rck.getPs())) {
            EmailKit.sendEmail("<EMAIL>;<EMAIL>;<EMAIL>", "面试概要:" + trueString(rck.getMc()) + ", 语言: " + trueString(rck.getYy()),
                    "名称：" + trueString(rck.getMc()) + "<br>" +
                            "电话：" + trueString(rck.getDh()) + "<br>" +
                            "邮箱：" + trueString(rck.getYx()) + "<br>" +
                            "语言：" + trueString(rck.getYy()) + "<br>" +
                            "出生：" + trueString(rck.getCsrq()) + "<br>" +
                            "创建日期：" + trueString(rck.getRq()) + "<br>" +
                            "评述：" + trueString(rck.getPs()) + "<br>" +
                            "链接：<a href='http://360.theolympiastone.com/my/rck/edit?id=" + trueString(rck.getId()) + "' target='_blank'>" + trueString(rck.getMc()) + "</a><br>"
            );
        }
    }

    public void edit() {
        keepPara();
        Rck rck = srv.findById(getParaToInt("id"));
        setAttr("rck", rck);
        setAttr("isAdd", false);
        String wj = trueString(rck.getWj());
        if (!StringUtils.isEmpty(wj)) {
            String[] wjList = trueString(wj).split(";");
            setAttr("wjList", wjList);
        }
        render("add_edit.html");
    }

    public void edit0() {
        Rck rck = srv.findById(getParaToInt("id"));
        setAttr("rck", rck);
        String wj = trueString(rck.getWj());
        if (!StringUtils.isEmpty(wj)) {
            String[] wjList = trueString(wj).split(";");
            setAttr("wjList", wjList);
        }
        render("edit0.html");
    }

    @Before(MyRckValidator.class)
    public void update() {
        List<UploadFile> files = getFiles();
        Rck rck = getBean(Rck.class);
        Integer id = rck.getId();
        Rck jRck = srv.findById(id);
        if (files != null) {
            String uploadPath = PathKit.getWebRootPath() + "/upload/rck/" + id;
            try {
                FileUtils.forceMkdir(new File(uploadPath));
            } catch (IOException e) {
                e.printStackTrace();
            }
            String jwj = jRck.getWj();
            StringBuilder wj = new StringBuilder(jwj);
            for (UploadFile file : files) {
                try {
                    String fileName = file.getFileName();
                    File destFile = new File(uploadPath + "/" + fileName);
                    FileUtils.deleteQuietly(destFile);
                    FileUtils.moveFile(file.getFile(), destFile);
                    wj.append(fileName).append(";");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            rck.setWj(wj.toString());
        }
        Ret ret = srv.update(rck);
        String ps = trueString(jRck.getPs());
        if (!ps.equalsIgnoreCase(rck.getPs())) {
            email(rck);
        }
        renderJson(ret.set("id", id));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void deleteFile() {
        try {
            Integer id = getParaToInt("id");
            String wj = getPara("wj");
            Rck rck = srv.findById(id);
            String rckWj = rck.getWj();
            rck.setWj(StringUtils.replace(rckWj, wj + ";", ""));
            rck.update();
            renderJson(Ret.ok().set("msg", "删除成功！"));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }

    public void uploadImage() {
        String path = get("path");
        String image = get("image");
        if (StringUtils.isEmpty(path)) {
            renderJson(Ret.fail().set("msg", "原图路径丢失，保存失败!"));
            return;
        }
        if (StringUtils.isEmpty(image)) {
            renderJson(Ret.fail().set("msg", "图像丢失，保存失败!"));
            return;
        }
        System.out.println(image.length());
        String[] split = image.split(",");
        String imgFilePath = PathKit.getWebRootPath() + path;
        if (split.length > 1) {
            Base64Utils.GenerateImage(split[1], imgFilePath);
        } else {
            Base64Utils.GenerateImage(image, imgFilePath);
        }
        renderJson(Ret.ok());
    }

    public void uploadFile() {
        List<UploadFile> files = getFiles();
        if (files == null || files.isEmpty()) {
            renderJson(Ret.fail().set("msg", "上传失败!"));
        }
        List<Rck> rckList = srv.findContanisWj();
        Map<String, Integer> fileMap = Maps.newHashMap();
        for (Rck record : rckList) {
            Integer id = record.getInt("id");
            String wj = trueString(record.getStr("wj"));
            if (!StringUtils.isEmpty(wj)) {
                String[] wjList = wj.split(";");
                for (String w : wjList) {
                    String filePath = PathKit.getWebRootPath() + "/upload/rck/" + id + "/" + w;
                    if (!new File(filePath).exists()) {
                        continue;
                    }
                    fileMap.put(calculateSHA256(filePath), id);
                }
            }
        }
        StringBuilder result = new StringBuilder();
        String scrq = get("scrq", yyyy_MM_dd());
        if (files != null) {
            for (UploadFile file : files) {
                try {
                    String fileHash256 = calculateSHA256(file.getFile());
                    if (fileMap.containsKey(fileHash256)) {
                        result.append(file.getOriginalFileName()).append(" 文件已存在! \n");
                        continue;
                    }
                    Rck rck = new Rck();
                    rck.setRq(scrq);
                    rck.save();
                    String uploadPath = PathKit.getWebRootPath() + "/upload/rck/" + rck.getId();
                    FileUtils.forceMkdir(new File(uploadPath));
                    String fileName = file.getFileName();
                    result.append(fileName).append(" 上传成功! \n");
                    rck.setMc(fileName.split("\\.")[0]);
                    File destFile = new File(uploadPath + "/" + fileName);
                    FileUtils.deleteQuietly(destFile);
                    FileUtils.moveFile(file.getFile(), destFile);
                    rck.setWj(fileName + ";");
                    rck.update();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        renderJson(Ret.ok().set("msg", "上传成功!").set("result", result));
    }

    public void changeValue() {
        Integer id = getParaToInt("id");
        String name = getPara("name");
        String value = getPara("value");
        Rck rck = srv.findById(id);
        rck.set(name, value);
        srv.update(rck);
        renderJson(Ret.ok("更改成功"));
    }
}
