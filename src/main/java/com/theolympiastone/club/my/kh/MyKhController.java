package com.theolympiastone.club.my.kh;

import com.google.common.collect.Lists;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.CacheKit;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.EmailKit;
import com.theolympiastone.club.common.kit.ExcelKit;
import com.theolympiastone.club.common.model.Kh;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.theolympiastone.club.common.CacheKit.KeyEnum.KH;
import static com.theolympiastone.club.common.CacheKit.KeyEnum.SZ;
import static com.theolympiastone.club.common.CacheKit.refreshCacheTable;
import static com.theolympiastone.club.common.kit.StringKit.urlDecode;
import static com.theolympiastone.club.common.kit.StringKit.yyyy_MM_dd;

@Before({FrontAuthInterceptor.class})
public class MyKhController extends BaseController {
    @Inject
    MyKhService srv;

    public void index() {
        keepPara();
        String query = get("query");
        Page<Kh> khPage = srv.paginate(getParaToInt("p", 1), getLoginAccount(), query);
        setAttr("page", khPage);
        render("index.html");
    }

    public void add() {
        setAttr("isAdd", true);
        render("add_edit.html");
    }

    @Before(MyKhValidator.class)
    public void save() {
        Kh kh = getBean(Kh.class, true);
        kh.setYwy(getLoginAccount().getUserName());
        kh.setHzywy(getLoginAccount().getUserName());
        kh.setDdgdywy(getLoginAccount().getUserName());
        if (StringUtils.isEmpty(kh.getHzsj())) {
            kh.setHzsj(yyyy_MM_dd());
        }
        Ret ret = srv.save(kh);
        refreshCacheTable(KH);
        renderJson(ret.set("id", kh.getId()));
    }

    public void checkDDUJsjl() {
        Integer id = getInt("id");
        String ddujxjl = get("ddujxjl");
        if (id == null) {
            renderJson(Ret.ok());
        } else {
            Record ddFirst = Db.findFirst("select 1 from dd where kh=?", id);
            if (ddFirst != null && StringUtils.isEmpty(ddujxjl)) {
                renderJson(Ret.fail().set("msg", "客户有订单，DDU驾驶距离，不能放空!"));
            } else {
                renderJson(Ret.ok());
            }
        }
    }


    public void edit() {
        keepPara();

        Kh kh = srv.findById(getParaToInt("id"));

        boolean noPermission = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"不看客户信息"});
        if(noPermission){
            renderJson(Ret.fail().set("msg", "没有权限访问该客户信息"));
            return;
        }
        // 权限检查：管理员可以查看所有客户，非管理员只能查看自己负责的客户
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (!hasRole) {
            String currentUserName = getLoginAccount().getUserName();
            String ywy = kh.getYwy();
            String hzywy = kh.getHzywy();

            boolean hasPermission = false;

            // 检查ywy字段是否包含当前用户
            if (!StringUtils.isEmpty(ywy)) {
                String[] ywyArray = ywy.split("[,;，；]");
                for (String email : ywyArray) {
                    if (currentUserName.equals(email.trim())) {
                        hasPermission = true;
                        break;
                    }
                }
            }

            // 检查hzywy字段是否包含当前用户
            if (!hasPermission && !StringUtils.isEmpty(hzywy)) {
                String[] hzywyArray = hzywy.split("[,;，；]");
                for (String email : hzywyArray) {
                    if (currentUserName.equals(email.trim())) {
                        hasPermission = true;
                        break;
                    }
                }
            }

            // 如果没有权限，返回错误信息
            if (!hasPermission) {
                renderJson(Ret.fail().set("msg", "没有权限访问该客户信息"));
                return;
            }
        }
        String gsmc = kh.getGsmc();
        List<Integer> yxkhList = com.google.common.collect.Lists.newArrayList();
        if (!StringUtils.isEmpty(gsmc)) {
            yxkhList = Db.query("select y.id id from kh k, yxkh y where k.gsmc=y.gsmc and y.gsmc like '%" + gsmc + "%'");
        }
        String documentPath = PathKit.getWebRootPath() + "/upload/" + "/kh/" + kh.getId() + "/";
        File personalDocumentDir = new File(documentPath);
        File[] documentFiles = personalDocumentDir.listFiles();
        List<String> documentFileList = Lists.newArrayList();
        if (documentFiles != null) {
            for (File f : documentFiles) {
                documentFileList.add(f.getName());
            }
        }
        setAttr("documentFileList", documentFileList);
        set("yxkhList", yxkhList);
        setAttr("kh", kh);
        setAttr("isAdd", false);
        render("add_edit.html");
    }

    @Before(MyKhValidator.class)
    public void update() {
        Kh kh = getBean(Kh.class, true);
        Ret ret = srv.update(kh);
        refreshCacheTable(KH);
        renderJson(ret.set("id", kh.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        refreshCacheTable(KH);
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void exportPrice() {
        String id = get("id");
        String jc = get("jc");
        List<Record> records = Db.find("select c.id id, c.mc mc,c.ywmc ywmc, k.jg jg, k.dw jgdw, if(isnull(jg) or jg='', '', '不变') czfs, k.kssj ksrq, k.jssj jsrq,k.id jgid " +
                "from cl c left join khszj k on c.id = k.szid and k.khid = ? " +
                "order by if(isnull(c.xh) or c.xh = '', 999999, c.xh + 0);", id);
        renderFile(ExcelKit.writeRecordListToTpl(jc + "客户石种价格导出.xls", "export/数据/客户石种价格导出.xls", records));
    }

    public void uploadKhjgFile() {
        UploadFile uploadFile = getFile();
        String khid = get("id");
        List<Map<Integer, String>> readList = ExcelKit.readList(uploadFile.getFile());
        List<String> updateList = Lists.newArrayList();
        StringBuilder sb = new StringBuilder();
        for (Map<Integer, String> lineMap : readList) {
            String szid = lineMap.get(0);
            String jg = lineMap.get(3);
            String dw = lineMap.get(4);
            String czfs = lineMap.get(5);
            String kssj = lineMap.get(6);
            String jssj = lineMap.get(7);
            String jgid = lineMap.get(8);
            if (StringUtils.isEmpty(czfs) || StringUtils.isEmpty(jg)) {
                continue;
            }
            if ("新增".equalsIgnoreCase(czfs)) {
                if (StringUtils.isEmpty(jgid)) {
                    updateList.add(String.format("insert into khszj (khid, szid, jg, dw, kssj, jssj) VALUES (%s, %s, '%s', '%s', '%s', '%s')", khid, szid, jg, dw, kssj, jssj));
                    sb.append("新增 石种:[").append(Objects.requireNonNull(CacheKit.getCacheTable(SZ).get(SZ, Integer.parseInt(szid))).getStr("mc")).append("] 单价.<br>");
                }
            } else if ("更新".equalsIgnoreCase(czfs)) {
                if (!StringUtils.isEmpty(jgid)) {
                    updateList.add(String.format("update khszj set khid=%s, szid=%s, jg='%s', dw='%s', kssj='%s', jssj='%s' where id=%s", khid, szid, jg, dw, kssj, jssj, jgid));
                    sb.append("更新 石种:[").append(Objects.requireNonNull(CacheKit.getCacheTable(SZ).get(SZ, Integer.parseInt(szid))).getStr("mc")).append("] 单价.<br>");
                }
            } else if ("删除".equalsIgnoreCase(czfs)) {
                if (!StringUtils.isEmpty(jgid)) {
                    updateList.add(String.format("delete from khszj where id=%s", jgid));
                    sb.append("删除 石种:[").append(Objects.requireNonNull(CacheKit.getCacheTable(SZ).get(SZ, Integer.parseInt(szid))).getStr("mc")).append("] 单价.<br>");
                }
            }
        }
        Db.tx(() -> {
            int[] batch = Db.batch(updateList, updateList.size());
            return true;
        });
        String ywyEmails = Db.queryStr("select ywy from kh where id=?", khid);
        EmailKit.sendEmail(Lists.newArrayList("<EMAIL>", "<EMAIL>", ywyEmails),
                "客户:[" + Objects.requireNonNull(CacheKit.getCacheTable(KH).get(KH, Integer.parseInt(khid))).getStr("jc") + "],  " + sb,
                "客户:[" + Objects.requireNonNull(CacheKit.getCacheTable(KH).get(KH, Integer.parseInt(khid))).getStr("jc") + "], " + sb + " <br /> By. " + getLoginAccount().getXm());
        renderJson(Ret.ok().set("msg", "导入成功!"));
    }

    public void documentUpload() {
        List<UploadFile> files = getFiles();
        Integer id = getInt("id");
        if (files == null || files.isEmpty()) {
            renderJson(Ret.fail().set("msg", "没有上传文件!"));
            return;
        }
        if (id == null) {
            renderJson(Ret.fail().set("msg", "用户ID为空，上传失败!"));
            return;
        }
        renderJson(srv.saveDocuments(id, files));
    }

    public void deleteFile() {
        Integer id = getInt("id");
        String fileName = urlDecode(get("fileName", ""));
        if (id == null) {
            renderJson(Ret.fail().set("msg", "客户ID为空，删除文件失败!"));
            return;
        }
        if (StringUtils.isEmpty(fileName)) {
            renderJson(Ret.fail().set("msg", "文件名字为空，删除文件失败!"));
            return;
        }
        FileUtils.deleteQuietly(new File(PathKit.getWebRootPath() + "/upload/" + "kh/" + id + "/" + fileName));
        renderJson(Ret.ok().set("msg", "文件[" + fileName + "]删除成功!"));
    }
}
