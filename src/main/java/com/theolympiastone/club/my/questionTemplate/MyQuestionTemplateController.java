package com.theolympiastone.club.my.questionTemplate;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.DataMapListener;
import com.theolympiastone.club.common.model.QuestionTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.theolympiastone.club.common.kit.ControllerKit.genFile;
import static com.theolympiastone.club.common.kit.StringKit.formatPureDate;
import static com.theolympiastone.club.common.kit.StringKit.yyyy_MM_dd;

@Before({FrontAuthInterceptor.class})
public class MyQuestionTemplateController extends BaseController {
    @Inject
    MyQuestionTemplateService srv;
    private static List<Map<String, String>> pxList = Lists.newArrayList();

    static {
        pxList.add(ImmutableMap.of("name", "按录入逆序", "value", " order by id desc"));
        pxList.add(ImmutableMap.of("name", "按录入顺序", "value", " order by id asc"));
    }

    public void index() {
        keepPara();
        Integer pageSize = getParaToInt("pageSize", 100);
        String query = getPara("query", "");
        String queryKsrq = getPara("queryKsrq", "");
        String queryJsrq = getPara("queryJsrq", "");
        String queryPx = getPara("queryPx", pxList.get(0).get("value"));
        setAttr("pageSize", pageSize);
        setAttr("query", query);
        setAttr("queryKsrq", queryKsrq);
        setAttr("queryJsrq", queryJsrq);
        setAttr("queryPx", queryPx);
        setAttr("pxList", pxList);
        Kv queryData = Kv.by("account", getLoginAccount()).set("query", query).set("queryKsrq", queryKsrq).set("queryJsrq", queryJsrq).set("queryPx", queryPx);
        Page<QuestionTemplate> questionTemplatePage = srv.paginate(getParaToInt("p", 1), pageSize, queryData);
        setAttr("page", questionTemplatePage);
        render("index.html");
    }

    public void saveOrUpdate() {
        String questionTemplateJson = get("questionTemplate");
        QuestionTemplate questionTemplate = new QuestionTemplate();
        // 将 JSON 字符串转换为 Map
        Map<String, Object> jsonMap = JsonKit.parse(questionTemplateJson, Map.class);
        // 将 Map 中的键值对设置到 QuestionTemplate 对象中
        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            questionTemplate.set(entry.getKey(), entry.getValue());
        }
        Integer id = questionTemplate.getId();
        Ret ret;
        if (id == null || id == 0) {
            questionTemplate.setCreateAt(yyyy_MM_dd());
            ret = srv.save(questionTemplate);
        } else {
            questionTemplate.setUpdateAt(yyyy_MM_dd());
            ret = srv.update(questionTemplate);
        }
        renderJson(ret.set("msg", "保存成功!").set("id", questionTemplate.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void uploadFile() {
        UploadFile file = getFile();
        DataMapListener<Map<Integer, Object>> dataListener = new DataMapListener<>(Lists.newArrayList(
                "name", "department", "cron_expression", "status", "create_at", "update_at"
        ));
        List<List<String>> head = new ArrayList<>();
        EasyExcel.read(file.getFile(), dataListener).head(head).sheet(0).doRead();
        List<Map<String, Object>> list = dataListener.getList();
        List<QuestionTemplate> questionTemplateList = Lists.newArrayList();
        for (Map<String, Object> map : list) {
            QuestionTemplate questionTemplate = new QuestionTemplate();
            questionTemplate._setOrPut(map);
            questionTemplate.setCreateAt(yyyy_MM_dd());
            questionTemplateList.add(questionTemplate);
        }
        Db.batchSave(questionTemplateList, 100);
        renderJson(Ret.ok().set("msg", "导入成功!"));
    }

    public void export() {
        String downloadFile = genFile("问题模板", "select * from questionTemplate order by id");
        redirect(downloadFile);
    }
}
