package com.theolympiastone.club.my.ylgrgz;

import com.alibaba.druid.util.StringUtils;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.model.Ylgrgz;

public class MyYlgrgzService {
    public static final MyYlgrgzService me = new MyYlgrgzService();
    private final Ylgrgz dao = new Ylgrgz().dao();


    private String getSuffixString(String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = "";
        if (!StringUtils.isEmpty(query)) {
            suffix += " and xm like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and yf >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and yf <= '" + queryJsrq + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Ylgrgz> paginate(int pageNum, String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = getSuffixString(query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, 100, "select * ", "from ylgrgz where 1=1 " + suffix + " ");
    }

    public Ret save(Ylgrgz ylgrgz) {
        ylgrgz.save();
        return Ret.ok("msg", "创建成功");
    }

    public Ylgrgz findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Ylgrgz ylgrgz) {
        ylgrgz.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
