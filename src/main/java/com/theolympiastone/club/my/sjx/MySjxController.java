package com.theolympiastone.club.my.sjx;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.DataMapListener;
import com.theolympiastone.club.common.model.Sjx;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.theolympiastone.club.common.kit.ControllerKit.genFile;
import static com.theolympiastone.club.common.kit.StringKit.formatPureDate;

@Before({FrontAuthInterceptor.class})
public class MySjxController extends BaseController {
    private static final List<Map<String, String>> pxList = Lists.newArrayList();
    private static final String uploadPath = PathKit.getWebRootPath() + "/upload/" + "sjx/";

    static {
        pxList.add(ImmutableMap.of("name", "按录入逆序", "value", " order by id desc"));
        pxList.add(ImmutableMap.of("name", "按录入顺序", "value", " order by id asc"));
    }

    @Inject
    MySjxService srv;

    public void sjx() {
        setAttr("sjxList", Db.find("select * from sjx order by sj desc"));
        render("sjx.html");
    }

    public void index() {
        keepPara();
        Integer pageSize = getParaToInt("pageSize", 100);
        String query = getPara("query", "");
        String queryKsrq = getPara("queryKsrq", "");
        String queryJsrq = getPara("queryJsrq", "");
        String queryPx = getPara("queryPx", pxList.get(0).get("value"));
        setAttr("pageSize", pageSize);
        setAttr("query", query);
        setAttr("queryKsrq", queryKsrq);
        setAttr("queryJsrq", queryJsrq);
        setAttr("queryPx", queryPx);
        setAttr("pxList", pxList);
        Page<Sjx> sjxPage = srv.paginate(getParaToInt("p", 1), pageSize, getLoginAccount(), query, queryKsrq, queryJsrq, queryPx);
        setAttr("page", sjxPage);
        render("index.html");
    }

    public void add() {
        keepPara();
        setAttr("isAdd", true);
        render("add_edit.html");
    }

    @Before(MySjxValidator.class)
    public void save() {
        Sjx sjx = getBean(Sjx.class, true);
        sjx.setCjr(getLoginAccount().getUserName());
        sjx.setCjsj(formatPureDate(new Date()));
        Ret ret = srv.save(sjx);
        renderJson(ret.set("msg", "保存成功!").set("id", sjx.getId()));
    }

    public void edit() {
        keepPara();
        Sjx sjx = srv.findById(getParaToInt("id"));
        setAttr("sjx", sjx);
        setAttr("isAdd", false);
        render("add_edit.html");
    }

    @Before(MySjxValidator.class)
    public void update() {
        Sjx sjx = getBean(Sjx.class, true);
        Ret ret = srv.update(sjx);
        renderJson(ret.set("msg", "更新成功!"));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void uploadPictures() {
        File directory = new File(uploadPath);
        try {
            FileUtils.forceMkdir(directory);
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<UploadFile> files = getFiles();
        StringBuilder filesResult = new StringBuilder();
        for (UploadFile file : files) {
            try {
                FileUtils.moveFileToDirectory(file.getFile(), directory, true);
            } catch (IOException e) {
                e.printStackTrace();
            }
            filesResult.append(file.getFileName()).append(";");
        }
        String[] split = filesResult.toString().split(";");
        renderJson(Ret.ok().set("files", StringUtils.join(split, ";")));
    }

    public void ysctp() {
        String tp = get("tp");
        render("ysctp.html");
    }

    public void uploadFile() {
        UploadFile file = getFile();
        DataMapListener<Map<Integer, Object>> dataListener = new DataMapListener<>(Lists.newArrayList(
                "sj", "zt", "nr", "cjr", "cjsj", "tp", "sp"
        ));
        List<List<String>> head = new ArrayList<>();
        EasyExcel.read(file.getFile(), dataListener).head(head).sheet(0).doRead();
        List<Map<String, Object>> list = dataListener.getList();
        List<Sjx> sjxList = Lists.newArrayList();
        for (Map<String, Object> map : list) {
            Sjx sjx = new Sjx();
            sjx._setOrPut(map);
            sjx.setCjr(getLoginAccount().getUserName());
            sjx.setCjsj(formatPureDate(new Date()));
            sjxList.add(sjx);
        }
        Db.batchSave(sjxList, 100);
        renderJson(Ret.ok().set("msg", "导入成功!"));
    }

    public void export() {
        String downloadFile = genFile("时间线", "select * from sjx order by id");
        redirect(downloadFile);
    }

    public void deleteFile() {
        Integer id = getInt("id");
        String wj = get("wj");
        FileUtils.deleteQuietly(new File(uploadPath + wj));
        Sjx sjx = srv.findById(id);
        String tp = sjx.getTp();
        String xtp = tp.replace(wj, "");
        sjx.setTp(xtp);
        sjx.save();
        renderJson(Ret.ok());
    }
}
