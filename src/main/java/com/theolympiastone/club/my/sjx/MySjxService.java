package com.theolympiastone.club.my.sjx;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Sjx;
import org.apache.commons.lang3.StringUtils;

public class MySjxService {
    public static final MySjxService me = new MySjxService();
    private final Sjx dao = new Sjx().dao();

    private String getSuffixString(Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        boolean hasRole = AdminAuthService.me.hasRole(account.getId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        String suffix = "";
        if (!hasRole) {
            suffix += " and cjr='" + account.getUserName() + "' ";
        }
        if (!StringUtils.isEmpty(query)) {
            suffix += " and mc like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and rq >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and rq <= '" + queryJsrq + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Sjx> paginate(int pageNum, int pageSize, Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = getSuffixString(account, query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, pageSize, "select * ", " from sjx where 1=1 " + suffix + " ");
    }

    public Ret save(Sjx sjx) {
        sjx.save();
        return Ret.ok("msg", "创建成功");
    }

    public Sjx findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Sjx sjx) {
        sjx.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
