package com.theolympiastone.club.my.order;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.OSConstants;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.TipHelperKit;
import com.theolympiastone.club.common.kit.EmailKit;
import com.theolympiastone.club.common.model.*;
import com.theolympiastone.club.my.common.RzKit;
import com.theolympiastone.club.my.dd.MyDdService;
import com.theolympiastone.club.my.dzhy.MyDzhyService;
import com.theolympiastone.club.my.sp.MySpService;
import net.m3u8.utils.StringUtils;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.theolympiastone.club.common.kit.DdgkKit.updateDdgk;
import static com.theolympiastone.club.common.kit.RequestKit.getIpAddr;
import static com.theolympiastone.club.common.kit.StringKit.trueString;
import static com.theolympiastone.club.common.kit.StringKit.yyyy_MM_dd;

/**
 * 我的项目
 */
@Before({FrontAuthInterceptor.class})
public class MyOrderController extends BaseController {
    @Inject
    private MyOrderService srv;
    @Inject
    private MyDdService ddSrv;
    @Inject
    private MyDzhyService dzhySrv;
    @Inject
    private MySpService spSrv;

    /**
     * 暂时全部显示，不分页
     */
    public void index() {
        keepPara();
        float sum = 0.0F;
        String queryJkid = getPara("queryJkid", "");
        String px = getPara("px", " order by d.cq desc");
        String kssj = getPara("kssj");
        String jssj = getPara("jssj");
        String hkqk = getPara("hkqk", "全部");
        String userName = getLoginAccount().getUserName();
        Page<Record> orderPage = srv.findNew(getLoginAccountId(), userName, getParaToInt("p", 1), queryJkid, kssj, jssj, hkqk, px);
        for (Record record : orderPage.getList()) {
            Float cha = record.getFloat("yszkrmb");
            cha = (cha == null) ? 0 : cha;
            sum += cha;
        }
        setAttr("queryJkid", queryJkid);
        setAttr("px", px);
        setAttr("hkqk", hkqk);
        setAttr("orderPage", orderPage);
        setAttr("sum", sum);
        render("index.html");
    }

    public void payment() {
        keepPara();
        String queryDdbh = getPara("queryDdbh");
        String kssj = getPara("kssj");
        String jssj = getPara("jssj");
        String khid = getPara("khid");
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (hasRole) {
            setAttr("khList", Db.find("select id,jc from kh order by jc"));
        } else {
            boolean wdsAdmin = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"WDS管理员"});
            setAttr("khList", Db.find("select id,jc from kh where ywy like '%" + getLoginAccount().getUserName() + "%' order by jc"));
        }
        if (StringUtils.isEmpty(queryDdbh) && StringUtils.isEmpty(khid)) {
            setAttr("list", Lists.newArrayList());
            render("payment.html");
        } else {
            String sqlWhere = " where lx='正常订单' and hkqk<>'已回全款' ";
            if (!StringUtils.isEmpty(queryDdbh)) {
                sqlWhere += " and ddbh like '%" + queryDdbh + "%' ";
            }
            if (!StringUtils.isEmpty(kssj)) {
                sqlWhere += " and ifnull(cq, current_date()) >= '" + kssj + "' ";
            }
            if (!StringUtils.isEmpty(jssj)) {
                sqlWhere += " and ifnull(cq, current_date()) <= '" + jssj + "' ";
            }
            if (!StringUtils.isEmpty(khid)) {
                sqlWhere += " and kh = " + khid + " ";
            }
            List<Record> list = Db.find("select d.ddbh,ifnull(d.cq, current_date()) cq,ifnull(round(d.yszk, 2), 0) yszk,hb,datediff(current_date(), ifnull(d.cq, current_date())) as zq, k.hksj_max hksj from dd d " +
                    " left join (SELECT " +
                    "    ddbh, " +
                    "    GREATEST( " +
                    "            IFNULL(hksj, 0), IFNULL(hksj1, 0), IFNULL(hksj2, 0), IFNULL(hksj3, 0), " +
                    "            IFNULL(hksj4, 0), IFNULL(hksj5, 0), IFNULL(hksj6, 0), IFNULL(hksj7, 0), " +
                    "            IFNULL(hksj8, 0), IFNULL(hksj9, 0), IFNULL(hksj10, 0), IFNULL(hksj11, 0), " +
                    "            IFNULL(hksj12, 0) " +
                    "    ) AS hksj_max " +
                    "FROM dd_kz) k on d.ddbh=k.ddbh " + sqlWhere + " order by d.ddbh, d.cq");
            setAttr("list", list);
            if (!list.isEmpty()) {
                setAttr("hb", OSConstants.hbMap.get(list.get(0).getStr("hb")));
                double sum = 0.0D;
                for (Record record : list) {
                    sum += record.getDouble("yszk");
                }
                setAttr("yszkTotal", sum);
            }
            render("payment.html");
        }

    }


    public void ddKz() {
        keepPara();
        String ddbh = getPara("ddbh");
        DdKz ddKz = srv.findByDdbh(ddbh);
        List<String> ejkmList = Db.query("select gzzfejkm1 ejkm from dd group by ejkm order by ejkm");
        List<String> kmList = Db.query("select gzzfkm1 km from dd group by km order by km");
        setAttr("ejkmList", ejkmList);
        setAttr("kmList", kmList);
        setAttr("dd", ddSrv.findByDdbh(ddbh));
        String today = yyyy_MM_dd();
        List<Record> whkhlRecords = Db.find("select * from whkhl where kssj<=? and jssj>=?", today, today);
        Map<String, String> whkhlMap = Maps.newHashMap();
        for (Record record : whkhlRecords) {
            whkhlMap.put(record.get("hb"), record.get("xs"));
        }
        setAttr("whkhlMap", whkhlMap);
        set("ddKz", ddKz);
        render("dd_kz.html");
    }

    public void ddkzUpdate() {
        keepPara();
        DdKz ddKz = getBean(DdKz.class, true);
        DdKz yDdkz = srv.findByDdbh(ddKz.getDdbh());

        // 检查回款信息是否有变化并发送邮件
        checkAndSendPaymentEmail(ddKz, yDdkz);

        ddKz.update();
        String ddbh = getPara("ddbh");
        String gzzf = getPara("gzzf");
        String gzzfje = getPara("gzzfje");
        String gzzfsj = getPara("gzzfsj");
        String gzzfje2 = getPara("gzzfje2");
        String gzzfsj2 = getPara("gzzfsj2");
        String gzzfje3 = getPara("gzzfje3");
        String gzzfsj3 = getPara("gzzfsj3");
        String gzzfje4 = getPara("gzzfje4");
        String gzzfsj4 = getPara("gzzfsj4");
        String gzzfje5 = getPara("gzzfje5");
        String gzzfsj5 = getPara("gzzfsj5");

        String gzzffs1 = getPara("gzzffs1");
        String gzzffs2 = getPara("gzzffs2");
        String gzzffs3 = getPara("gzzffs3");
        String gzzffs4 = getPara("gzzffs4");
        String gzzffs5 = getPara("gzzffs5");

        String gzzfkm1 = getPara("gzzfkm1");
        String gzzfkm2 = getPara("gzzfkm2");
        String gzzfkm3 = getPara("gzzfkm3");
        String gzzfkm4 = getPara("gzzfkm4");
        String gzzfkm5 = getPara("gzzfkm5");

        String gzzfejkm1 = getPara("gzzfejkm1");
        String gzzfejkm2 = getPara("gzzfejkm2");
        String gzzfejkm3 = getPara("gzzfejkm3");
        String gzzfejkm4 = getPara("gzzfejkm4");
        String gzzfejkm5 = getPara("gzzfejkm5");
        Dd dd = ddSrv.findByDdbh(ddbh);
        dd.setGzzfje(gzzfje);
        dd.setGzzfsj(gzzfsj);
        dd.setGzzfje2(gzzfje2);
        dd.setGzzfsj2(gzzfsj2);
        dd.setGzzfje3(gzzfje3);
        dd.setGzzfsj3(gzzfsj3);
        dd.setGzzfje4(gzzfje4);
        dd.setGzzfsj4(gzzfsj4);
        dd.setGzzfje5(gzzfje5);
        dd.setGzzfsj5(gzzfsj5);
        dd.setGzzffs1(gzzffs1);
        dd.setGzzffs2(gzzffs2);
        dd.setGzzffs3(gzzffs3);
        dd.setGzzffs4(gzzffs4);
        dd.setGzzffs5(gzzffs5);
        dd.setGzzfkm1(gzzfkm1);
        dd.setGzzfkm2(gzzfkm2);
        dd.setGzzfkm3(gzzfkm3);
        dd.setGzzfkm4(gzzfkm4);
        dd.setGzzfkm5(gzzfkm5);
        dd.setGzzfejkm1(gzzfejkm1);
        dd.setGzzfejkm2(gzzfejkm2);
        dd.setGzzfejkm3(gzzfejkm3);
        dd.setGzzfejkm4(gzzfejkm4);
        dd.setGzzfejkm5(gzzfejkm5);
        dd.setGzzf(gzzf);
        dd.setCq(ddKz.getCq());
        dd.setWhkhl(ddKz.getWhkhl());
        dd.update();
        Dzhy dzhy = dzhySrv.findByDdbh(ddbh);
        if (dzhy != null) {
            dzhy.setCfrq(ddKz.getCq());
        }
        updateDdgk();
        RzKit.ddrz(ddbh, "订单编号: " + ddbh + ", 修改状态.", getLoginAccountId(), getIpAddr(getRequest()));
        renderJson(Ret.ok("更新成功!").set("ddbh", ddbh));
    }


    private void sendEmail(String ddbh) {
        new Thread(() -> {
            try {
                Thread.sleep(600000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            TipHelperKit.sendEmail("提醒:收款-阿海+业务员", ddbh, Lists.newArrayList(), getLoginAccount().getUserName());
        }).start();
    }

    public void delete() {
        keepPara();
        String jkid = getPara("jkid");
        srv.delete(jkid);
        RzKit.ddrz(jkid, "订单编号: " + jkid + ", 修改状态.", getLoginAccountId(), getIpAddr(getRequest()));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void savecsmx() {
        String jkid = getPara("jkid");
        String csmx = getPara("csmx");
        srv.savecsmx(jkid, csmx, getLoginAccount());
        renderJson(Ret.ok());
    }

    public void remark() {
        keepPara();
        String jkid = getPara("jkid");
        String lmid = getPara("lmid");
        setAttr("jkid", jkid);
        setAttr("lmid", lmid);
        List<Xmjkrz> replyList = srv.getReplyList(jkid, lmid);
        setAttr("replyList", replyList);
        render("remark.html");
    }

    public void saveRemark() {
        if (notLogin()) {
            renderJson(Ret.fail("登录后才可以记录备注"));
            return;
        }
        String replyContent = getPara("replyContent");
        if (StrKit.isBlank(replyContent)) {
            renderJson(Ret.fail("回复内容不能为空"));
            return;
        }
        String jkid = getPara("jkid");
        String lmid = getPara("lmid");
        Ret ret = srv.saveRemark(jkid, lmid, getLoginAccountId(), replyContent);

        Account loginAccount = getLoginAccount();
        ret.set("loginAccount", loginAccount);
        String replyItem = renderToString("_reply_item.html", ret);
        RzKit.ddrz(jkid, "订单编号: " + jkid + " -> 类目: " + lmid + ", 填写备注.", getLoginAccountId(), getIpAddr(getRequest()));

        ret.set("replyItem", replyItem);
        renderJson(ret);
    }

    public void deleteRemark() {
        keepPara();
        if (isLogin()) {
            int replyId = getParaToInt("id");
            srv.deleteShareReplyById(replyId);
            renderJson(Ret.ok());
        } else {
            renderJson(Ret.fail("未登录用户不会显示删除链接，请勿手工制造请求"));
        }
    }

    public void log() {
        keepPara();
        String jkid = getPara("jkid");
        List<Jklsrz> jklsrzList = srv.getJklsrzList(jkid);
        setAttr("jkid", jkid);
        setAttr("jklsrzList", jklsrzList);
        render("log.html");
    }

    public void claim() {
        String jkid = getPara("jkid");
        Page<Sp> spPage = spSrv.ddPaginate(jkid, getParaToInt("p", 1));
        setAttr("page", spPage);
        Float zspje = 0.0F;
        for (Sp sp : spPage.getList()) {
            Float spje = sp.getSpje();
            if (spje == null) {
                continue;
            }
            DdKz ddKz = srv.findByDdbh(jkid);
            Float sphl = Float.parseFloat(trueString(ddKz.getWhkhl(), "0"));
            zspje += sphl * spje;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        df.setRoundingMode(RoundingMode.HALF_UP);
        keepPara();
        setAttr("zspje", df.format(zspje));
        render("claim.html");
    }

    private String getYfFile() {
        String uploadPath = PathKit.getWebRootPath() + "/upload/" + "yf/";
        try {
            File directory = new File(uploadPath);
            if (!directory.exists()) {
                FileUtils.forceMkdir(directory);
            }
            UploadFile file = getFile();
            if (file == null) {
                return "";
            }
            String fileName = file.getFileName();
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            String newFileName = UUID.randomUUID() + fileType;
            FileUtils.moveFile(file.getFile(), new File(uploadPath + newFileName));
            return newFileName;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 检查回款信息是否有变化并发送邮件
     */
    private void checkAndSendPaymentEmail(DdKz newDdKz, DdKz oldDdKz) {
        if (oldDdKz == null) {
            return;
        }

        String ddbh = newDdKz.getDdbh();
        boolean hasChanges = false;

        // 检查hkje到hkje12是否有变化
        String[] hkjeFields = {"hkje", "hkje1", "hkje2", "hkje3", "hkje4", "hkje5",
                              "hkje6", "hkje7", "hkje8", "hkje9", "hkje10", "hkje11", "hkje12"};

        for (String field : hkjeFields) {
            String newValue = getFieldValue(newDdKz, field);
            String oldValue = getFieldValue(oldDdKz, field);
            if (!trueString(newValue).equals(trueString(oldValue))) {
                hasChanges = true;
                break;
            }
        }

        if (hasChanges) {
            // 获取客户的业务员邮箱
            String hzywy = Db.queryStr("select k.hzywy from kh k, dd d where k.id = d.kh and d.ddbh = ?", ddbh);
            if (!StringUtils.isEmpty(hzywy)) {
                sendPaymentNotificationEmail(ddbh, newDdKz, hzywy);
            }
        }
    }

    /**
     * 获取DdKz对象指定字段的值
     */
    private String getFieldValue(DdKz ddKz, String fieldName) {
        switch (fieldName) {
            case "hkje": return ddKz.getHkje();
            case "hkje1": return ddKz.getHkje1();
            case "hkje2": return ddKz.getHkje2();
            case "hkje3": return ddKz.getHkje3();
            case "hkje4": return ddKz.getHkje4();
            case "hkje5": return ddKz.getHkje5();
            case "hkje6": return ddKz.getHkje6();
            case "hkje7": return ddKz.getHkje7();
            case "hkje8": return ddKz.getHkje8();
            case "hkje9": return ddKz.getHkje9();
            case "hkje10": return ddKz.getHkje10();
            case "hkje11": return ddKz.getHkje11();
            case "hkje12": return ddKz.getHkje12();
            default: return "";
        }
    }

    /**
     * 发送回款信息通知邮件
     */
    private void sendPaymentNotificationEmail(String ddbh, DdKz ddKz, String hzywy) {
        try {
            // 构建邮件主题
            String subject = "订单编号 " + ddbh + " 有新回款信息";

            // 构建邮件内容
            StringBuilder content = new StringBuilder();
            content.append("<h3>订单回款信息更新通知</h3>");
            content.append("<p><strong>订单编号：</strong>").append(ddbh).append("</p>");
            content.append("<p><strong>总金额：</strong>").append(trueString(ddKz.getZje(), "0")).append("</p>");

            // 计算总回款金额
            BigDecimal totalPayment = BigDecimal.ZERO;
            content.append("<h4>回款明细：</h4>");
            content.append("<table border='1' style='border-collapse: collapse; width: 100%;'>");
            content.append("<tr><th>回款次数</th><th>回款时间</th><th>回款金额</th></tr>");

            // 处理每次回款信息
            String[] hkjeFields = {"hkje", "hkje1", "hkje2", "hkje3", "hkje4", "hkje5",
                                  "hkje6", "hkje7", "hkje8", "hkje9", "hkje10", "hkje11", "hkje12"};
            String[] hksjFields = {"hksj", "hksj1", "hksj2", "hksj3", "hksj4", "hksj5",
                                  "hksj6", "hksj7", "hksj8", "hksj9", "hksj10", "hksj11", "hksj12"};

            for (int i = 0; i < hkjeFields.length; i++) {
                String amount = getFieldValue(ddKz, hkjeFields[i]);
                String time = getHksjFieldValue(ddKz, hksjFields[i]);

                if (!StringUtils.isEmpty(amount) && !amount.equals("0")) {
                    try {
                        BigDecimal paymentAmount = new BigDecimal(amount);
                        totalPayment = totalPayment.add(paymentAmount);

                        content.append("<tr>");
                        content.append("<td>第").append(i == 0 ? "1" : String.valueOf(i + 1)).append("次</td>");
                        content.append("<td>").append(trueString(time, "未填写")).append("</td>");
                        content.append("<td>").append(amount).append("</td>");
                        content.append("</tr>");
                    } catch (NumberFormatException e) {
                        // 忽略无效的数字格式
                    }
                }
            }

            content.append("</table>");
            content.append("<p><strong>总回款金额：</strong>").append(totalPayment.toString()).append("</p>");
            content.append("<br><p>此邮件由系统自动发送，请勿回复。</p>");

            // 发送邮件
            EmailKit.sendEmail(hzywy, subject, content.toString());

        } catch (Exception e) {
            e.printStackTrace();
            // 记录错误日志但不影响主流程
        }
    }

    /**
     * 获取DdKz对象指定回款时间字段的值
     */
    private String getHksjFieldValue(DdKz ddKz, String fieldName) {
        switch (fieldName) {
            case "hksj": return ddKz.getHksj();
            case "hksj1": return ddKz.getHksj1();
            case "hksj2": return ddKz.getHksj2();
            case "hksj3": return ddKz.getHksj3();
            case "hksj4": return ddKz.getHksj4();
            case "hksj5": return ddKz.getHksj5();
            case "hksj6": return ddKz.getHksj6();
            case "hksj7": return ddKz.getHksj7();
            case "hksj8": return ddKz.getHksj8();
            case "hksj9": return ddKz.getHksj9();
            case "hksj10": return ddKz.getHksj10();
            case "hksj11": return ddKz.getHksj11();
            case "hksj12": return ddKz.getHksj12();
            default: return "";
        }
    }
}