package com.theolympiastone.club.data;

import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;
import us.codecraft.webmagic.Page;
import us.codecraft.webmagic.Site;
import us.codecraft.webmagic.processor.PageProcessor;
import us.codecraft.webmagic.selector.Html;

public class WuCiDownload implements PageProcessor {

    static Jedis jedis = new Jedis("127.0.0.1", 6379);
    private final Site site = Site.me().setSleepTime(2000)
            .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36");

    public static void main(String[] args) {
        //https://www.nihaowua.com/
//        jedis.auth("olympiastone");
//        Spider.create(new WuCiDownload())
//                .addUrl("http://www.nihaowua.com/")
//                .thread(1)
//                .start();

        for (int i = 0; i < 10000; i++) {

            try {
                String content = HttpClientUtil.get(HttpConfig.custom().url("http://www.nihaowua.com"));
                Html html = new Html(content);
                String wuci = html.xpath("//section//allText()").toString();
                if (!StringUtils.isEmpty(wuci)) {
                    System.out.println(wuci);
                    jedis.sadd("nihaowua", wuci);
                }
                Thread.sleep(2000L);
            } catch (HttpProcessException | InterruptedException e) {
                e.printStackTrace();
            }
        }


    }

    @Override
    public void process(Page page) {
        page.addTargetRequest("http://www.nihaowua.com/");
        String content = page.getHtml().xpath("//section//allText()").toString();
        if (!StringUtils.isEmpty(content)) {
            jedis.sadd("nihaowua", content);
        }
    }

    @Override
    public Site getSite() {
        return site;
    }
}
