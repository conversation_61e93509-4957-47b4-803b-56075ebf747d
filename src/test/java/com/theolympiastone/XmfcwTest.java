package com.theolympiastone;

import com.google.common.collect.Lists;
import org.apache.commons.io.FileUtils;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Set;

import static com.theolympiastone.club.common.kit.StringKit.trueString;

public class XmfcwTest {
    public static void main(String[] args) throws IOException {
        Jedis jedis = new Jedis();
//        Set<String> dkeys = jedis.keys("xmfcw_电子城_*");
//        for (String key : dkeys) {
//            jedis.del(key);
//        }
//
//        if (true) {
//            return;
//        }
        Set<String> keys = jedis.keys("xmfcw_电子城_*");
        List<String> resultList = Lists.newArrayList("坐落,室号,性质,用途,拟售价格,面积,权属状态,销售状态");
        for (String key : keys) {
            // 坐落 室号 性质 用途 拟售价格 面积 权属状态
            String hget1 = trueString(jedis.hget(key, "坐落"));
            String hget2 = trueString(jedis.hget(key, "室号"));
            String hget3 = trueString(jedis.hget(key, "性质"));
            String hget4 = trueString(jedis.hget(key, "用途"));
            String hget5 = trueString(jedis.hget(key, "拟售价格"));
            String hget6 = trueString(jedis.hget(key, "面积"));
            String hget7 = trueString(jedis.hget(key, "权属状态"));
            String hget8 = trueString(jedis.hget(key, "销售状态"));
            resultList.add(hget1 + "," + hget2 + "," + hget3 + "," + hget4 + "," + hget5 + "," + hget6 + "," + hget7 + "," + hget8);
        }
        FileUtils.writeLines(new File("/Users/<USER>/Desktop/dzc.csv"), resultList);
        jedis.close();
    }
}
