drop PROCEDURE `sclrsb`;
DELIMITER $$
CREATE PROCEDURE `sclrsb`(IN i_kssj varchar(16), IN i_jssj varchar(16), IN i_user_id int, OUT o_id varchar(128))
BEGIN
    declare o_yysr varchar(16);
    declare o_yycb varchar(16);
    declare o_hz varchar(16);
    declare o_qtywsr varchar(16);
    declare o_qtywcb varchar(16);
    declare o_nggewsr varchar(16);
    declare o_nggewfy varchar(16);
    declare o_qtsr varchar(16);
    declare o_yyfy varchar(16);
    declare o_gzf varchar(16);
    declare o_gz varchar(16);
    declare o_cwfy varchar(16);
    declare o_scjyfy varchar(16);
    declare o_jmbfy varchar(16);
    declare o_lrze varchar(16);
    declare o_yqndsytz varchar(16);
    declare o_sr varchar(16);
    declare o_cb varchar(16);
    declare o_sp varchar(16);
    declare o_wfplrqcs varchar(16);
    declare o_wfplrqms varchar(16);
    declare o_sntqlr varchar(16);
    declare o_dtqlr varchar(16);
    declare v_ksyf varchar(8);
    declare v_jsyf varchar(8);
    declare v_ksn varchar(4);
    declare v_jsn varchar(4);
    declare v_bxje varchar(32);
    declare v_yycb varchar(32);
    select substr(i_kssj, 1, 4) into v_ksn;
    select substr(i_jssj, 1, 4) into v_jsn;
    select substr(i_kssj, 1, 7) into v_ksyf;
    select substr(i_jssj, 1, 7) into v_jsyf;

    select round(sum(abs(ifnull(je,0))), 2) into o_nggewsr from (
                                                                    select je from vw_sr where km='NGG额外收入' and sj>= i_kssj and sj<=i_jssj
                                                                    union all
                                                                    select je from vw_zf where km='NGG额外收入' and sj>= i_kssj and sj<=i_jssj) a;

    select round(sum(abs(ifnull(je,0))), 2) into o_nggewfy from (
                                                                    select je from vw_sr where km='NGG额外费用' and sj>= i_kssj and sj<=i_jssj
                                                                    union all
                                                                    select je from vw_zf where km='NGG额外费用' and sj>= i_kssj and sj<=i_jssj) a;

    select concat(round(sum(d.hkermb), 2)),
           concat(
                   round(sum(round(ifnull(d.clcb, 0), 2)+ round(ifnull(d.wjg, 0), 2)+ round(ifnull(w.dm_fj, 0), 2)+ round(ifnull(w.rm_fj, 0), 2)+ round(ifnull(w.zx_fj, 0), 2)+ round(ifnull(w.cl_fj, 0), 2)), 2)
           )
    into o_yysr,v_yycb
    from dd d
             left join dd_kz w on d.ddbh = w.ddbh
    where d.cq >= i_kssj
      and d.cq <= i_jssj
      and d.lx = '正常订单';

    select concat(round(sum(round(ifnull(hkermb, 0), 2)), 2)) into o_qtywsr from (
                                                                                     select hkermb
                                                                                     from dd
                                                                                     where cq >= i_kssj
                                                                                       and cq <= i_jssj
                                                                                       and lx = '其他代垫'
                                                                                       and cq >= '2023-01-01'
                                                                                     union all
                                                                                     select hkermb
                                                                                     from dd
                                                                                     where lrsj >= i_kssj
                                                                                       and lrsj <= i_jssj
                                                                                       and lx = '其他代垫'
                                                                                       and (cq is null or cq<'2023-01-01')) a;


    select concat(round(0 - sum(round(ifnull(bxje,0),2)), 2))
    into o_qtsr
    from bx
    where fylb = '其他收入'
      and bxsj >= i_kssj
      and bxsj <= i_jssj;

    select concat(round(sum(0-round(ifnull(zfje, 0), 2)), 2))
    into o_qtywcb
    from vw_wcbx_zf
    where fylb = '其他业务成本'
      and zfsj >= i_kssj
      and zfsj <= i_jssj;

    select concat(round(sum(round(ifnull(bxje, 0), 2)), 2))
    into v_bxje
    from (select bxje
          from bx
          where fylb = '成本'
            and bxsj >= i_kssj
            and bxsj <= i_jssj
            and (sfkn <> '是'
              or sfkn is null)
          union all
          select bxje
          from gcbx
          where fylb = '成本'
            and bxsj >= i_kssj
            and bxsj <= i_jssj) a;

    select concat(round(sum(je), 2))
    into o_yyfy
    from (select round(sum(round(ifnull(bxje, 0), 2)), 2) je
          from bx
          where fylb = '营业费用'
            and bxsj >= i_kssj
            and bxsj <= i_jssj
          union all
          select round(sum(round(ifnull(zj, 0), 2)), 2) je
          from gz
          where yf >= v_ksyf
            and yf <= v_jsyf
            and ifnull(cylr, '0') <> '1'
          union all
          select round(sum(round(ifnull(jg, 0), 2)), 2) je
          from kdf
          where rq >= i_kssj
            and rq <= i_jssj
          union all
          select round(sum(round(ifnull(yzf, 0), 2)), 2) je
          from dd
          where cq >= i_kssj
            and cq <= i_jssj
            and lx = '正常订单') a;

    select round(sum(round(ifnull(yzf, 0), 2)), 2) je into o_gzf from dd where cq >= i_kssj and cq <= i_jssj;

    select concat(round(sum(round(ifnull(zj, 0), 2)), 2)) je
    into o_gz
    from gz
    where yf >= v_ksyf
      and yf <= v_jsyf
      and ifnull(cylr, '0') <> '1';

    select concat(round(sum(round(ifnull(je, 0), 2)), 2))
    into o_cwfy
    from (select je
          from vw_sr
          where ejkm = '手续费'
            and sj >= i_kssj
            and sj <= i_jssj
          union all
          select je
          from vw_zf
          where ejkm = '手续费'
            and sj >= i_kssj
            and sj <= i_jssj) a;

    select concat(round(sum(round(ifnull(bxje, 0), 2)), 2)) into o_jmbfy from bx where jtfy = '集美办费用' and bxsj >= i_kssj and bxsj <= i_jssj;
    select concat(round(sum(round(ifnull(bxje, 0), 2)), 2)) into o_scjyfy from bx where jtfy = '熵尘经营费用' and bxsj >= i_kssj and bxsj <= i_jssj;

    select concat(round(sum(round(ifnull(bxje, 0), 2)), 2))
    into o_sr
    from bx
    where fylb = '收入'
      and bxsj >= i_kssj
      and bxsj <= i_jssj
      and sfkn = '是';

    select round(sum(round(ifnull(s.spje,0) * if(s.jedw = '元', 1, w.whkhl), 2)), 2)
    into o_sp
    from sp s,
         dd d,
         vw_dd_whkhl w
    where s.jkid = d.ddbh
      and s.jkid = w.ddbh
      and s.spsj >= i_kssj
      and s.spsj <= i_jssj
      and s.sfkn = '是'
      and s.cllx = '扣款';

    select concat(round(sum(round(ifnull(bxje, 0), 2)), 2))
    into o_cb
    from bx
    where fylb = '成本'
      and bxsj >= i_kssj
      and bxsj <= i_jssj
      and sfkn = '是';

    select round(sum(ifnull(hkjermb, 0)), 2) hzrmb into o_hz
    from (select d.ddbh, d.hksj, d.hkfs, round(ifnull(d.hkje, 0) * ifnull(v.sjjkhl, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj is not null
            and d.hksj <> ''
            and d.hkfs = '坏账'
          union all
          select d.ddbh, d.hksj1 hksj, d.hkfs1 hkfs, round(ifnull(d.hkje1, 0) * ifnull(v.sjjkhl1, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj1 is not null
            and d.hksj1 <> ''
            and d.hkfs1 = '坏账'
          union all
          select d.ddbh, d.hksj2 hksj, d.hkfs2 hkfs, round(ifnull(d.hkje2, 0) * ifnull(v.sjjkhl2, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj2 is not null
            and d.hksj2 <> ''
            and d.hkfs2 = '坏账'
          union all
          select d.ddbh, d.hksj3 hksj, d.hkfs3 hkfs, round(ifnull(d.hkje3, 0) * ifnull(v.sjjkhl3, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj3 is not null
            and d.hksj3 <> ''
            and d.hkfs3 = '坏账'
          union all
          select d.ddbh, d.hksj4 hksj, d.hkfs4 hkfs, round(ifnull(d.hkje4, 0) * ifnull(v.sjjkhl4, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj4 is not null
            and d.hksj4 <> ''
            and d.hkfs4 = '坏账'
          union all
          select d.ddbh, d.hksj5 hksj, d.hkfs5 hkfs, round(ifnull(d.hkje5, 0) * ifnull(v.sjjkhl5, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj5 is not null
            and d.hksj5 <> ''
            and d.hkfs5 = '坏账'
          union all
          select d.ddbh, d.hksj6 hksj, d.hkfs6 hkfs, round(ifnull(d.hkje6, 0) * ifnull(v.sjjkhl6, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj6 is not null
            and d.hksj6 <> ''
            and d.hkfs6 = '坏账'
          union all
          select d.ddbh, d.hksj7 hksj, d.hkfs7 hkfs, round(ifnull(d.hkje7, 0) * ifnull(v.sjjkhl7, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj7 is not null
            and d.hksj7 <> ''
            and d.hkfs7 = '坏账'
          union all
          select d.ddbh, d.hksj8 hksj, d.hkfs8 hkfs, round(ifnull(d.hkje8, 0) * ifnull(v.sjjkhl8, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj8 is not null
            and d.hksj8 <> ''
            and d.hkfs8 = '坏账'
          union all
          select d.ddbh, d.hksj9 hksj, d.hkfs9 hkfs, round(ifnull(d.hkje9, 0) * ifnull(v.sjjkhl9, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj9 is not null
            and d.hksj9 <> ''
            and d.hkfs9 = '坏账'
          union all
          select d.ddbh, d.hksj10 hksj, d.hkfs10 hkfs, round(ifnull(d.hkje10, 0) * ifnull(v.sjjkhl10, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj10 is not null
            and d.hksj10 <> ''
            and d.hkfs10 = '坏账'
          union all
          select d.ddbh, d.hksj11 hksj, d.hkfs11 hkfs, round(ifnull(d.hkje11, 0) * ifnull(v.sjjkhl11, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj11 is not null
            and d.hksj11 <> ''
            and d.hkfs11 = '坏账'
          union all
          select d.ddbh, d.hksj12 hksj, d.hkfs12 hkfs, round(ifnull(d.hkje12, 0) * ifnull(v.sjjkhl12, 0), 2) hkjermb
          from dd_kz d
                   left join vw_dd_hkhl v on d.ddbh = v.ddbh
          where d.hksj12 is not null
            and d.hksj12 <> ''
            and d.hkfs12 = '坏账') a
    where a.hksj >= i_kssj and a.hksj <= i_jssj;

    select concat(round(sum(round(ifnull(sz, 0), 2)), 2)) into o_wfplrqcs from cwsj where mc = '未分配利润期初数' and nf = v_ksn;
    select concat(round(sum(round(ifnull(sz, 0), 2)), 2)) into o_sntqlr from cwsj where mc = '上年提取利润' and nf = v_ksn;

    select ifnull(v_bxje, 0) into v_bxje;
    select ifnull(v_yycb, 0) into v_yycb;
    select ifnull(o_yysr, 0) into o_yysr;
    select ifnull(o_yycb, 0) into o_yycb;
    select ifnull(o_qtywsr, 0) into o_qtywsr;
    select ifnull(o_qtywcb, 0) into o_qtywcb;
    select ifnull(o_qtsr, 0) into o_qtsr;
    select ifnull(o_yyfy, 0) into o_yyfy;
    select ifnull(o_gzf, 0) into o_gzf;
    select ifnull(o_gz, 0) into o_gz;
    select ifnull(o_cwfy, 0) into o_cwfy;
    select ifnull(o_jmbfy, 0) into o_jmbfy;
    select ifnull(o_scjyfy, 0) into o_scjyfy;
    select ifnull(o_lrze, 0) into o_lrze;
    select ifnull(o_sr, 0) into o_sr;
    select ifnull(o_cb, 0) into o_cb;
    select ifnull(o_sp, 0) into o_sp;
    select ifnull(o_wfplrqcs, 0) into o_wfplrqcs;
    select ifnull(o_sntqlr, 0) into o_sntqlr;
    select ifnull(o_hz, 0) into o_hz;

    select round(ifnull(v_yycb, 0) + ifnull(v_bxje, 0), 2) into o_yycb;
    select round(ifnull(o_nggewsr, 0) - ifnull(o_nggewfy, 0) + ifnull(o_yysr, 0) - ifnull(o_yycb, 0) + ifnull(o_qtywsr, 0) - ifnull(o_qtywcb, 0) + ifnull(o_qtsr, 0) - ifnull(o_yyfy, 0) - ifnull(o_cwfy, 0) - ifnull(o_scjyfy, 0) - ifnull(o_hz, 0), 2) into o_lrze;

    select round(o_sr + o_cb + o_sp, 2) into o_yqndsytz;

    select round(o_lrze + o_wfplrqcs, 2) into o_wfplrqms;

    select round(o_wfplrqms - o_sntqlr, 2) into o_dtqlr;
    #     select UNIX_TIMESTAMP(curdate()) into today;
    #     UPDATE coupon SET status=0 WHERE endtime < today;
#     delete from lr where kssj = i_kssj and jssj = i_jssj;
    insert into lr (kssj, jssj, yysr, yycb, hz, qtywsr, qtywcb, qtsr, yyfy, gzf, gz, cwfy, jmbfy,scjyfy, lrze, yqndsytz, sr, cb, sp, wfplrqcs, wfplrqms, sntqlr, dtqlr, ngg_ewsr, ngg_ewfy, create_id, create_time)
    values (i_kssj, i_jssj, o_yysr, o_yycb, o_hz, o_qtywsr, o_qtywcb, o_qtsr,
            o_yyfy, o_gzf, o_gz, o_cwfy, o_jmbfy, o_scjyfy,o_lrze, o_yqndsytz, o_sr,
            o_cb, o_sp, o_wfplrqcs, o_wfplrqms, o_sntqlr, o_dtqlr,
            o_nggewsr, o_nggewfy, i_user_id, now());
    SET o_id = LAST_INSERT_ID();
END;
$$

DELIMITER ;
